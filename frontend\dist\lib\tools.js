import { mcpClient } from './mcp/mcp-client';
// 本地工具定义已删除，现在使用MCP服务器提供的工具
/**
 * 清理参数以符合Ollama要求
 * 移除Ollama不支持的JSON Schema字段
 */
function cleanParametersForOllama(inputSchema) {
    if (!inputSchema || typeof inputSchema !== 'object') {
        return {
            type: 'object',
            properties: {},
            required: []
        };
    }
    const cleaned = { ...inputSchema };
    // 移除Ollama不支持的字段
    delete cleaned.$schema;
    delete cleaned.$id;
    delete cleaned.$ref;
    delete cleaned.definitions;
    delete cleaned.additionalProperties;
    // 确保必需字段存在
    if (!cleaned.type)
        cleaned.type = 'object';
    if (!cleaned.properties)
        cleaned.properties = {};
    if (!cleaned.required)
        cleaned.required = [];
    return cleaned;
}
/**
 * 将MCP工具转换为Ollama格式
 */
function convertMcpToolToOllamaFormat(mcpTool) {
    return {
        type: 'function',
        function: {
            name: mcpTool.name,
            description: mcpTool.description || '',
            parameters: cleanParametersForOllama(mcpTool.inputSchema)
        }
    };
}
/**
 * 验证工具是否符合Ollama要求
 */
function validateOllamaTool(tool) {
    try {
        return (tool.type === 'function' &&
            typeof tool.function.name === 'string' &&
            tool.function.name.length > 0 &&
            typeof tool.function.description === 'string' &&
            tool.function.parameters &&
            tool.function.parameters.type === 'object' &&
            Array.isArray(tool.function.parameters.required));
    }
    catch (error) {
        console.warn('工具验证失败:', error);
        return false;
    }
}
// 保留测试工具用于模型兼容性检查
export const testTool = {
    type: 'function',
    function: {
        name: 'test_tool',
        description: '测试工具调用功能',
        parameters: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    description: '测试消息'
                }
            },
            required: ['message']
        }
    }
};
// 所有可用工具的集合 - 现在从MCP服务器动态获取
export let availableTools = [];
/**
 * 从MCP服务器获取可用工具列表
 */
export async function loadAvailableTools() {
    try {
        // 确保MCP客户端已连接
        if (!mcpClient.isClientConnected()) {
            const connected = await mcpClient.connect();
            if (!connected) {
                console.warn('无法连接到MCP服务器，使用空工具列表');
                return [];
            }
        }
        // 获取MCP工具并转换为Ollama工具格式
        const mcpTools = await mcpClient.getAvailableTools();
        availableTools = mcpTools
            .map(tool => convertMcpToolToOllamaFormat(tool))
            .filter(tool => validateOllamaTool(tool));
        console.log(`已从MCP服务器加载 ${availableTools.length} 个工具`);
        return availableTools;
    }
    catch (error) {
        console.error('加载MCP工具失败:', error);
        return [];
    }
}
// 根据工具名称获取工具定义
export async function getToolsByNames(toolNames) {
    console.log('getToolsByNames 被调用，请求的工具:', toolNames);
    // 在服务器端，从mcpServerClient和多服务器客户端获取工具
    if (typeof window === 'undefined') {
        const { mcpServerClient } = require('./mcp/mcp-client-server');
        const { multiServerMcpClient } = require('./mcp/mcp-multi-server-client');
        const allTools = [];
        const toolMap = new Map(); // 用于去重
        // 首先尝试从数据库直接获取工具
        try {
            console.log('尝试从数据库获取工具...');
            const response = await fetch('http://localhost:3000/api/mcp/tools', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.tools && data.tools.length > 0) {
                    console.log('从数据库获取到工具:', data.tools.map((t) => t.name));
                    const dbTools = data.tools
                        .map((tool) => convertMcpToolToOllamaFormat(tool))
                        .filter((tool) => validateOllamaTool(tool));
                    dbTools.forEach((tool) => {
                        toolMap.set(tool.function.name, tool);
                    });
                    console.log('从数据库转换的工具:', dbTools.map((t) => t.function.name));
                }
            }
        }
        catch (error) {
            console.error('从数据库获取工具失败:', error);
        }
        // 1. 获取原有MCP工具
        try {
            const mcpTools = mcpServerClient.getAvailableTools();
            const serverTools = mcpTools
                .map((tool) => convertMcpToolToOllamaFormat(tool))
                .filter((tool) => validateOllamaTool(tool));
            // 添加到工具映射中进行去重
            serverTools.forEach((tool) => {
                toolMap.set(tool.function.name, tool);
            });
            console.log('从原有MCP服务器获取到工具:', serverTools.map((t) => t.function.name));
        }
        catch (error) {
            console.error('获取原有MCP工具失败:', error);
        }
        // 2. 获取多服务器MCP工具
        try {
            // 首先加载MCP配置
            console.log('加载MCP服务器配置...');
            const fs = require('fs').promises;
            const path = require('path');
            const configPath = path.join(process.cwd(), 'mcp-servers.json');
            let config = {};
            try {
                const configData = await fs.readFile(configPath, 'utf-8');
                const parsedConfig = JSON.parse(configData);
                config = parsedConfig.mcpServers || {};
                console.log('加载的MCP配置:', Object.keys(config));
            }
            catch (configError) {
                console.warn('无法加载MCP配置文件:', configError);
            }
            // 设置配置并强制连接和刷新所有服务器工具
            multiServerMcpClient.setConfig(config);
            console.log('强制连接和刷新多服务器客户端...');
            await multiServerMcpClient.connectAll();
            await multiServerMcpClient.refreshAllTools();
            const multiServerTools = multiServerMcpClient.getAllAvailableTools();
            console.log('从多服务器客户端获取到的原始工具:', multiServerTools);
            const convertedMultiServerTools = multiServerTools
                .map((tool) => convertMcpToolToOllamaFormat(tool))
                .filter((tool) => validateOllamaTool(tool));
            // 添加到工具映射中进行去重
            convertedMultiServerTools.forEach((tool) => {
                toolMap.set(tool.function.name, tool);
            });
            console.log('转换后的多服务器工具:', convertedMultiServerTools.map((t) => t.function.name));
        }
        catch (error) {
            console.error('获取多服务器MCP工具失败:', error);
        }
        // 将去重后的工具转换为数组
        allTools.push(...Array.from(toolMap.values()));
        console.log('所有可用工具:', allTools.map((t) => t.function.name));
        // 如果从数据库获取到了工具，优先使用数据库中的工具
        if (toolMap.size > 0) {
            console.log('使用数据库中的工具，工具数量:', toolMap.size);
            // 过滤用户请求的工具
            const filteredTools = toolNames
                .map(name => toolMap.get(name))
                .filter((tool) => tool !== undefined);
            const foundToolNames = filteredTools.map((t) => t.function.name);
            const missingTools = toolNames.filter(name => !foundToolNames.includes(name));
            console.log('从数据库找到的工具:', foundToolNames);
            if (missingTools.length > 0) {
                console.warn('数据库中未找到的工具:', missingTools);
            }
            return filteredTools;
        }
        // 过滤用户请求的工具，并记录不存在的工具
        console.log('开始过滤工具，请求的工具名称:', toolNames);
        console.log('所有可用工具名称:', allTools.map((t) => t.function.name));
        const filteredTools = allTools.filter((tool) => {
            const isIncluded = toolNames.includes(tool.function.name);
            console.log(`工具 ${tool.function.name} 是否匹配:`, isIncluded);
            return isIncluded;
        });
        const foundToolNames = filteredTools.map((t) => t.function.name);
        const missingTools = toolNames.filter(name => !foundToolNames.includes(name));
        if (missingTools.length > 0) {
            console.warn('以下工具不存在:', missingTools);
            console.warn('可能的原因: 工具名称不匹配或服务器未连接');
        }
        console.log('过滤后的工具:', filteredTools.map((t) => t.function.name));
        console.log('过滤后的工具数量:', filteredTools.length);
        return filteredTools;
    }
    // 在客户端，使用缓存的availableTools
    const filteredTools = availableTools.filter(tool => toolNames.includes(tool.function.name));
    console.log('客户端过滤后的工具:', filteredTools.map((t) => t.function.name));
    return filteredTools;
}
/**
 * 初始化MCP工具系统
 */
export async function initializeMcpTools() {
    try {
        const success = await mcpClient.connect();
        if (success) {
            console.log('MCP工具系统初始化成功');
            await refreshMcpTools();
        }
        else {
            console.warn('MCP工具系统初始化失败');
        }
        return success;
    }
    catch (error) {
        console.error('MCP工具系统初始化出错:', error);
        return false;
    }
}
/**
 * 刷新MCP工具列表
 */
export async function refreshMcpTools() {
    try {
        if (mcpClient.isClientConnected()) {
            const mcpTools = await mcpClient.refreshTools();
            console.log(`已刷新 ${mcpTools.length} 个MCP工具`);
        }
    }
    catch (error) {
        console.error('刷新MCP工具失败:', error);
    }
}
/**
 * 获取所有可用工具（包括原有MCP工具和多服务器MCP工具）
 */
export async function getAllAvailableTools() {
    const tools = [];
    // 0. 添加本地预设工具
    const localTools = [
        {
            type: 'function',
            function: {
                name: 'calculate',
                description: '执行数学计算',
                parameters: {
                    type: 'object',
                    properties: {
                        expression: {
                            type: 'string',
                            description: '要计算的数学表达式，支持基本的四则运算'
                        }
                    },
                    required: ['expression']
                }
            }
        },
        {
            type: 'function',
            function: {
                name: 'get_current_time',
                description: '获取当前日期和时间',
                parameters: {
                    type: 'object',
                    properties: {},
                    required: []
                }
            }
        }
    ];
    tools.push(...localTools);
    try {
        // 1. 获取原有的MCP工具
        if (!mcpClient.isClientConnected()) {
            await mcpClient.connect();
        }
        const mcpTools = mcpClient.getAvailableTools();
        const convertedMcpTools = mcpTools
            .map((tool) => convertMcpToolToOllamaFormat(tool))
            .filter((tool) => validateOllamaTool(tool));
        tools.push(...convertedMcpTools);
        // 2. 获取多服务器MCP工具
        try {
            const response = await fetch('/api/mcp/tools');
            const data = await response.json();
            if (data.success && data.tools) {
                const multiServerTools = data.tools
                    .map((tool) => {
                    // 为多服务器工具添加服务器名称标识
                    const toolWithServerInfo = {
                        ...tool,
                        description: tool.description + (tool.serverName ? ` (来自 ${tool.serverName})` : '')
                    };
                    return convertMcpToolToOllamaFormat(toolWithServerInfo);
                })
                    .filter((tool) => validateOllamaTool(tool));
                tools.push(...multiServerTools);
            }
        }
        catch (error) {
            console.error('获取多服务器MCP工具失败:', error);
        }
    }
    catch (error) {
        console.error('获取MCP工具失败:', error);
    }
    // 去重：根据工具名称去除重复项，保留最后一个（多服务器工具优先）
    const uniqueTools = tools.reduce((acc, current) => {
        const existingIndex = acc.findIndex(tool => tool.function.name === current.function.name);
        if (existingIndex >= 0) {
            // 如果已存在同名工具，替换为当前工具（后加载的优先）
            acc[existingIndex] = current;
        }
        else {
            acc.push(current);
        }
        return acc;
    }, []);
    console.log(`去重后获取到 ${uniqueTools.length} 个工具`);
    return uniqueTools;
}
/**
 * 检查工具是否可用（检查MCP工具）
 */
export async function isToolAvailable(toolName) {
    try {
        // 确保MCP客户端已连接
        if (!mcpClient.isClientConnected()) {
            await mcpClient.connect();
        }
        return mcpClient.isToolAvailable(toolName);
    }
    catch (error) {
        console.error('检查MCP工具可用性失败:', error);
        return false;
    }
}
// 工具执行函数
export class ToolExecutor {
    /**
     * 执行工具调用（仅使用MCP工具）
     */
    static async executeToolCall(toolName, args) {
        try {
            // 只使用MCP工具
            return await this.executeMcpTool(toolName, args);
        }
        catch (error) {
            return `工具执行失败: ${error instanceof Error ? error.message : '未知错误'}`;
        }
    }
    /**
     * 执行MCP工具调用
     */
    static async executeMcpTool(toolName, args) {
        try {
            // 在服务器端使用mcpServerClient和多服务器客户端，在客户端使用mcpClient
            if (typeof window === 'undefined') {
                // 服务器端
                const { mcpServerClient } = require('./mcp/mcp-client-server');
                const { multiServerMcpClient } = require('./mcp/mcp-multi-server-client');
                // 首先尝试从原有MCP服务器调用工具
                try {
                    if (!mcpServerClient.isClientConnected()) {
                        await mcpServerClient.connect();
                    }
                    if (mcpServerClient.isToolAvailable(toolName)) {
                        const result = await mcpServerClient.callTool({
                            name: toolName,
                            arguments: args || {}
                        });
                        if (!result.isError && result.content) {
                            const textContent = result.content
                                .filter((item) => item.type === 'text')
                                .map((item) => item.text)
                                .join('\n');
                            return textContent || '工具执行成功';
                        }
                    }
                }
                catch (error) {
                    console.log('原有MCP服务器调用失败，尝试多服务器客户端:', error);
                }
                // 如果原有MCP服务器没有该工具或调用失败，尝试多服务器客户端
                try {
                    await multiServerMcpClient.connectAll();
                    const result = await multiServerMcpClient.callTool(toolName, args || {});
                    if (!result.isError && result.content) {
                        const textContent = result.content
                            .filter((item) => item.type === 'text')
                            .map((item) => item.text)
                            .join('\n');
                        return textContent || '工具执行成功';
                    }
                    else {
                        throw new Error(result.content?.[0]?.text || '工具调用失败');
                    }
                }
                catch (error) {
                    console.error('多服务器MCP工具调用失败:', error);
                    throw new Error('所有MCP服务器都无法执行该工具');
                }
            }
            else {
                // 客户端 - 保持原有逻辑
                if (!mcpClient.isClientConnected()) {
                    const connected = await mcpClient.connect();
                    if (!connected) {
                        throw new Error('无法连接到MCP服务器');
                    }
                }
                const result = await mcpClient.callTool({
                    name: toolName,
                    arguments: args || {}
                });
                if (!result.isError && result.content) {
                    const textContent = result.content
                        .filter(item => item.type === 'text')
                        .map(item => item.text)
                        .join('\n');
                    return textContent || '工具执行成功';
                }
                else {
                    throw new Error(result.content?.[0]?.text || '工具调用失败');
                }
            }
        }
        catch (error) {
            console.error('MCP工具调用失败:', error);
            throw error;
        }
    }
}
// 本地工具实现已删除，现在使用真正的MCP工具
