"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleCheckBig\", [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-x.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CircleX)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst CircleX = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleX\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15 9-6 6\",\n            key: \"1uzhvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 9 6 6\",\n            key: \"z0biqf\"\n        }\n    ]\n]);\n //# sourceMappingURL=circle-x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxjQUFRLGdFQUFnQixDQUFDLE9BQVM7SUFDdEM7UUFBQyxRQUFVO1FBQUE7WUFBRSxFQUFJO1lBQU0sQ0FBSSxRQUFNO1lBQUEsQ0FBRztZQUFNLEdBQUs7UUFBQSxDQUFVO0tBQUE7SUFDekQ7UUFBQyxVQUFZO1FBQUE7WUFBRSxRQUFRLENBQW9CO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMzRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxzcmNcXGljb25zXFxjbG9jay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENsb2NrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhNaUEySURFeUlERXlJREUySURFMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Nsb2NrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2xvY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDbG9jaycsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvY2s7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* harmony import */ var _ToolCallVisualization__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToolCallVisualization */ \"(app-pages-browser)/./src/app/chat/components/ToolCallVisualization.tsx\");\n/* harmony import */ var _ToolContentDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ToolContentDisplay */ \"(app-pages-browser)/./src/app/chat/components/ToolContentDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\n\n\nfunction MessageItem(param) {\n    let { message, showAIStatus, aiState } = param;\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    // 解析工具调用内容，创建步骤数据\n    const parseToolCallSteps = (content)=>{\n        const steps = [];\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') && content.includes('参数:') && content.includes('结果:')) {\n            const toolCallMatches = content.match(/调用工具:\\s*([^\\n]+)\\n参数:\\s*([^]*?)\\n\\n结果:\\s*([^]*?)(?=\\n\\n|$)/g);\n            if (toolCallMatches) {\n                toolCallMatches.forEach((match, index)=>{\n                    const toolNameMatch = match.match(/调用工具:\\s*([^\\n]+)/);\n                    const paramsMatch = match.match(/参数:\\s*([^]*?)\\n\\n结果:/);\n                    const resultMatch = match.match(/结果:\\s*([^]*?)(?=\\n\\n|$)/);\n                    if (toolNameMatch) {\n                        const toolName = toolNameMatch[1].trim();\n                        let params = {};\n                        let result = '';\n                        try {\n                            if (paramsMatch) {\n                                params = JSON.parse(paramsMatch[1].trim());\n                            }\n                        } catch (e) {\n                            var _paramsMatch_;\n                            params = {\n                                raw: (paramsMatch === null || paramsMatch === void 0 ? void 0 : (_paramsMatch_ = paramsMatch[1]) === null || _paramsMatch_ === void 0 ? void 0 : _paramsMatch_.trim()) || ''\n                            };\n                        }\n                        if (resultMatch) {\n                            result = resultMatch[1].trim();\n                        }\n                        // 添加执行步骤\n                        steps.push({\n                            id: \"execution-\".concat(index),\n                            type: 'execution',\n                            toolName,\n                            status: 'success',\n                            timestamp: new Date(),\n                            input: params\n                        });\n                        // 添加结果步骤\n                        steps.push({\n                            id: \"result-\".concat(index),\n                            type: 'result',\n                            toolName,\n                            status: result.includes('错误') || result.includes('失败') ? 'error' : 'success',\n                            timestamp: new Date(),\n                            output: result,\n                            error: result.includes('错误') || result.includes('失败') ? result : undefined\n                        });\n                    }\n                });\n            }\n        }\n        return steps;\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 116,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理工具调用结果的显示\n    const renderToolContent = (content)=>{\n        try {\n            const toolData = JSON.parse(content);\n            if (toolData.tool_name && toolData.result) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_4__.ToolResultSummary, {\n                    result: toolData.result,\n                    isSuccess: !content.includes('错误') && !content.includes('失败'),\n                    executionTime: toolData.execution_time\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this);\n            }\n        } catch (e) {\n            // 如果不是JSON格式，检查是否是简单的工具结果\n            if (content.includes('工具执行') || content.includes('调用工具')) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_4__.ToolContentDisplay, {\n                    content: content,\n                    type: \"output\",\n                    title: \"工具执行结果\",\n                    maxLines: 6\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息中的工具调用\n    const renderAssistantContent = (content)=>{\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') || content.includes('```json') && content.includes('tool_calls')) {\n            const toolSteps = parseToolCallSteps(content);\n            if (toolSteps.length > 0) {\n                // 分离工具调用部分和普通文本部分\n                const toolCallPattern = /调用工具:[^]*?(?=\\n\\n(?!参数:|结果:)|$)/g;\n                const textParts = content.split(toolCallPattern).filter((part)=>part.trim());\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolCallVisualization__WEBPACK_IMPORTED_MODULE_3__.ToolCallVisualization, {\n                            steps: toolSteps,\n                            isLoading: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        textParts.map((part, index)=>part.trim() && !part.includes('调用工具:') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(part.trim())\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 17\n                            }, this) : null)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this);\n            }\n            // 处理旧格式的JSON工具调用\n            if (content.includes('```json') && content.includes('tool_calls')) {\n                const parts = content.split('```');\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: parts.map((part, index)=>{\n                        if (part.startsWith('json') && part.includes('tool_calls')) {\n                            try {\n                                var _toolCall_tool_calls__function, _toolCall_tool_calls_, _toolCall_tool_calls, _toolCall_tool_calls__function1, _toolCall_tool_calls_1, _toolCall_tool_calls1;\n                                const jsonContent = part.replace('json\\n', '').trim();\n                                const toolCall = JSON.parse(jsonContent);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_4__.ToolContentDisplay, {\n                                    content: ((_toolCall_tool_calls = toolCall.tool_calls) === null || _toolCall_tool_calls === void 0 ? void 0 : (_toolCall_tool_calls_ = _toolCall_tool_calls[0]) === null || _toolCall_tool_calls_ === void 0 ? void 0 : (_toolCall_tool_calls__function = _toolCall_tool_calls_.function) === null || _toolCall_tool_calls__function === void 0 ? void 0 : _toolCall_tool_calls__function.arguments) || {},\n                                    type: \"input\",\n                                    title: \"调用工具: \".concat((_toolCall_tool_calls1 = toolCall.tool_calls) === null || _toolCall_tool_calls1 === void 0 ? void 0 : (_toolCall_tool_calls_1 = _toolCall_tool_calls1[0]) === null || _toolCall_tool_calls_1 === void 0 ? void 0 : (_toolCall_tool_calls__function1 = _toolCall_tool_calls_1.function) === null || _toolCall_tool_calls__function1 === void 0 ? void 0 : _toolCall_tool_calls__function1.name),\n                                    maxLines: 4\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 21\n                                }, this);\n                            } catch (e) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"whitespace-pre-wrap\",\n                                    children: part\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 26\n                                }, this);\n                            }\n                        }\n                        return part.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: renderImageIfUrl(part)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 36\n                        }, this) : null;\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 209,\n            columnNumber: 12\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 \".concat(isUser ? 'bg-blue-600 text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'),\n                        children: [\n                            showAIStatus && aiState && aiState.status === 'tool_calling' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner-small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-green-700 dark:text-green-300\",\n                                        children: aiState.toolName ? \"正在调用 \".concat(aiState.toolName, \"...\") : '正在调用工具...'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(message.content)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 324,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9jb21wb25lbnRzL01lc3NhZ2VJdGVtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDNkI7QUFHQztBQUNRO0FBRWE7QUFRdEUsU0FBU1MsWUFBWSxLQUFvRDtRQUFwRCxFQUFFQyxPQUFPLEVBQUVDLFlBQVksRUFBRUMsT0FBTyxFQUFvQixHQUFwRDtJQUMxQixNQUFNQyxTQUFTSCxRQUFRSSxJQUFJLEtBQUs7SUFDaEMsTUFBTUMsY0FBY0wsUUFBUUksSUFBSSxLQUFLO0lBQ3JDLE1BQU1FLFNBQVNOLFFBQVFJLElBQUksS0FBSztJQUVoQyxrQkFBa0I7SUFDbEIsTUFBTUcscUJBQXFCLENBQUNDO1FBQzFCLE1BQU1DLFFBQWUsRUFBRTtRQUV2QixlQUFlO1FBQ2YsSUFBSUQsUUFBUUUsUUFBUSxDQUFDLFlBQVlGLFFBQVFFLFFBQVEsQ0FBQyxVQUFVRixRQUFRRSxRQUFRLENBQUMsUUFBUTtZQUNuRixNQUFNQyxrQkFBa0JILFFBQVFJLEtBQUssQ0FBQztZQUV0QyxJQUFJRCxpQkFBaUI7Z0JBQ25CQSxnQkFBZ0JFLE9BQU8sQ0FBQyxDQUFDRCxPQUFPRTtvQkFDOUIsTUFBTUMsZ0JBQWdCSCxNQUFNQSxLQUFLLENBQUM7b0JBQ2xDLE1BQU1JLGNBQWNKLE1BQU1BLEtBQUssQ0FBQztvQkFDaEMsTUFBTUssY0FBY0wsTUFBTUEsS0FBSyxDQUFDO29CQUVoQyxJQUFJRyxlQUFlO3dCQUNqQixNQUFNRyxXQUFXSCxhQUFhLENBQUMsRUFBRSxDQUFDSSxJQUFJO3dCQUN0QyxJQUFJQyxTQUFTLENBQUM7d0JBQ2QsSUFBSUMsU0FBUzt3QkFFYixJQUFJOzRCQUNGLElBQUlMLGFBQWE7Z0NBQ2ZJLFNBQVNFLEtBQUtDLEtBQUssQ0FBQ1AsV0FBVyxDQUFDLEVBQUUsQ0FBQ0csSUFBSTs0QkFDekM7d0JBQ0YsRUFBRSxPQUFPSyxHQUFHO2dDQUNNUjs0QkFBaEJJLFNBQVM7Z0NBQUVLLEtBQUtULENBQUFBLHdCQUFBQSxtQ0FBQUEsZ0JBQUFBLFdBQWEsQ0FBQyxFQUFFLGNBQWhCQSxvQ0FBQUEsY0FBa0JHLElBQUksT0FBTTs0QkFBRzt3QkFDakQ7d0JBRUEsSUFBSUYsYUFBYTs0QkFDZkksU0FBU0osV0FBVyxDQUFDLEVBQUUsQ0FBQ0UsSUFBSTt3QkFDOUI7d0JBRUEsU0FBUzt3QkFDVFYsTUFBTWlCLElBQUksQ0FBQzs0QkFDVEMsSUFBSSxhQUFtQixPQUFOYjs0QkFDakJjLE1BQU07NEJBQ05WOzRCQUNBVyxRQUFROzRCQUNSQyxXQUFXLElBQUlDOzRCQUNmQyxPQUFPWjt3QkFDVDt3QkFFQSxTQUFTO3dCQUNUWCxNQUFNaUIsSUFBSSxDQUFDOzRCQUNUQyxJQUFJLFVBQWdCLE9BQU5iOzRCQUNkYyxNQUFNOzRCQUNOVjs0QkFDQVcsUUFBUVIsT0FBT1gsUUFBUSxDQUFDLFNBQVNXLE9BQU9YLFFBQVEsQ0FBQyxRQUFRLFVBQVU7NEJBQ25Fb0IsV0FBVyxJQUFJQzs0QkFDZkUsUUFBUVo7NEJBQ1JhLE9BQU9iLE9BQU9YLFFBQVEsQ0FBQyxTQUFTVyxPQUFPWCxRQUFRLENBQUMsUUFBUVcsU0FBU2M7d0JBQ25FO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLE9BQU8xQjtJQUNUO0lBRUEsYUFBYTtJQUNiLE1BQU0yQixtQkFBbUIsQ0FBQ0M7UUFDeEIsTUFBTUMsZ0JBQWdCO1FBQ3RCLE1BQU1DLFVBQVVGLEtBQUt6QixLQUFLLENBQUMwQjtRQUUzQixJQUFJQyxTQUFTO1lBQ1gsTUFBTUMsUUFBUUgsS0FBS0ksS0FBSyxDQUFDSDtZQUN6QixxQkFDRSw4REFBQ0k7Z0JBQUlDLFdBQVU7MEJBQ1pILE1BQU1JLEdBQUcsQ0FBQyxDQUFDQyxNQUFNL0I7b0JBQ2hCLElBQUl3QixjQUFjUSxJQUFJLENBQUNELE9BQU87d0JBQzVCLHFCQUNFLDhEQUFDSDs0QkFBZ0JDLFdBQVU7OzhDQUN6Qiw4REFBQ0k7b0NBQ0NDLEtBQUtIO29DQUNMSSxLQUFJO29DQUNKTixXQUFVO29DQUNWTyxTQUFTLENBQUMxQjs0Q0FFUkE7d0NBREFBLEVBQUUyQixhQUFhLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO3lDQUNoQzdCLHNDQUFBQSxFQUFFMkIsYUFBYSxDQUFDRyxrQkFBa0IsY0FBbEM5QiwwREFBQUEsb0NBQW9DK0IsU0FBUyxDQUFDQyxNQUFNLENBQUM7b0NBQ3ZEOzs7Ozs7OENBRUYsOERBQUNkO29DQUFJQyxXQUFVOzt3Q0FBc0M7d0NBQzFDRTs7Ozs7Ozs7MkJBWEgvQjs7Ozs7b0JBZWQ7b0JBQ0EsT0FBTytCLHFCQUFPLDhEQUFDWTtrQ0FBa0JaO3VCQUFSL0I7Ozs7K0JBQXVCO2dCQUNsRDs7Ozs7O1FBR047UUFFQSxxQkFBTyw4REFBQzJDO3NCQUFNcEI7Ozs7OztJQUNoQjtJQUVBLGNBQWM7SUFDZCxNQUFNcUIsb0JBQW9CLENBQUNsRDtRQUN6QixJQUFJO1lBQ0YsTUFBTW1ELFdBQVdyQyxLQUFLQyxLQUFLLENBQUNmO1lBQzVCLElBQUltRCxTQUFTQyxTQUFTLElBQUlELFNBQVN0QyxNQUFNLEVBQUU7Z0JBQ3pDLHFCQUNFLDhEQUFDdkIsa0VBQWlCQTtvQkFDaEJ1QixRQUFRc0MsU0FBU3RDLE1BQU07b0JBQ3ZCd0MsV0FBVyxDQUFDckQsUUFBUUUsUUFBUSxDQUFDLFNBQVMsQ0FBQ0YsUUFBUUUsUUFBUSxDQUFDO29CQUN4RG9ELGVBQWVILFNBQVNJLGNBQWM7Ozs7OztZQUc1QztRQUNGLEVBQUUsT0FBT3ZDLEdBQUc7WUFDViwwQkFBMEI7WUFDMUIsSUFBSWhCLFFBQVFFLFFBQVEsQ0FBQyxXQUFXRixRQUFRRSxRQUFRLENBQUMsU0FBUztnQkFDeEQscUJBQ0UsOERBQUNiLG1FQUFrQkE7b0JBQ2pCVyxTQUFTQTtvQkFDVG9CLE1BQUs7b0JBQ0xvQyxPQUFNO29CQUNOQyxVQUFVOzs7Ozs7WUFHaEI7UUFDRjtRQUNBLHFCQUFPLDhEQUFDdkI7WUFBSUMsV0FBVTtzQkFBdUJQLGlCQUFpQjVCOzs7Ozs7SUFDaEU7SUFFQSxlQUFlO0lBQ2YsTUFBTTBELHlCQUF5QixDQUFDMUQ7UUFDOUIsZUFBZTtRQUNmLElBQUlBLFFBQVFFLFFBQVEsQ0FBQyxZQUFhRixRQUFRRSxRQUFRLENBQUMsY0FBY0YsUUFBUUUsUUFBUSxDQUFDLGVBQWdCO1lBQ2hHLE1BQU15RCxZQUFZNUQsbUJBQW1CQztZQUVyQyxJQUFJMkQsVUFBVUMsTUFBTSxHQUFHLEdBQUc7Z0JBQ3hCLGtCQUFrQjtnQkFDbEIsTUFBTUMsa0JBQWtCO2dCQUN4QixNQUFNQyxZQUFZOUQsUUFBUWlDLEtBQUssQ0FBQzRCLGlCQUFpQkUsTUFBTSxDQUFDMUIsQ0FBQUEsT0FBUUEsS0FBSzFCLElBQUk7Z0JBRXpFLHFCQUNFLDhEQUFDdUI7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDL0MseUVBQXFCQTs0QkFDcEJhLE9BQU8wRDs0QkFDUEssV0FBVzs7Ozs7O3dCQUlaRixVQUFVMUIsR0FBRyxDQUFDLENBQUNDLE1BQU0vQixRQUNwQitCLEtBQUsxQixJQUFJLE1BQU0sQ0FBQzBCLEtBQUtuQyxRQUFRLENBQUMseUJBQzVCLDhEQUFDZ0M7Z0NBQWdCQyxXQUFVOzBDQUN4QlAsaUJBQWlCUyxLQUFLMUIsSUFBSTsrQkFEbkJMOzs7O3VDQUdSOzs7Ozs7O1lBSVo7WUFFQSxpQkFBaUI7WUFDakIsSUFBSU4sUUFBUUUsUUFBUSxDQUFDLGNBQWNGLFFBQVFFLFFBQVEsQ0FBQyxlQUFlO2dCQUNqRSxNQUFNOEIsUUFBUWhDLFFBQVFpQyxLQUFLLENBQUM7Z0JBQzVCLHFCQUNFLDhEQUFDQztvQkFBSUMsV0FBVTs4QkFDWkgsTUFBTUksR0FBRyxDQUFDLENBQUNDLE1BQU0vQjt3QkFDaEIsSUFBSStCLEtBQUs0QixVQUFVLENBQUMsV0FBVzVCLEtBQUtuQyxRQUFRLENBQUMsZUFBZTs0QkFDMUQsSUFBSTtvQ0FNV2dFLGdDQUFBQSx1QkFBQUEsc0JBRU9BLGlDQUFBQSx3QkFBQUE7Z0NBUHBCLE1BQU1DLGNBQWM5QixLQUFLK0IsT0FBTyxDQUFDLFVBQVUsSUFBSXpELElBQUk7Z0NBQ25ELE1BQU11RCxXQUFXcEQsS0FBS0MsS0FBSyxDQUFDb0Q7Z0NBQzVCLHFCQUNFLDhEQUFDOUUsbUVBQWtCQTtvQ0FFakJXLFNBQVNrRSxFQUFBQSx1QkFBQUEsU0FBU0csVUFBVSxjQUFuQkgsNENBQUFBLHdCQUFBQSxvQkFBcUIsQ0FBQyxFQUFFLGNBQXhCQSw2Q0FBQUEsaUNBQUFBLHNCQUEwQkksUUFBUSxjQUFsQ0oscURBQUFBLCtCQUFvQ0ssU0FBUyxLQUFJLENBQUM7b0NBQzNEbkQsTUFBSztvQ0FDTG9DLE9BQU8sU0FBa0QsUUFBekNVLHdCQUFBQSxTQUFTRyxVQUFVLGNBQW5CSCw2Q0FBQUEseUJBQUFBLHFCQUFxQixDQUFDLEVBQUUsY0FBeEJBLDhDQUFBQSxrQ0FBQUEsdUJBQTBCSSxRQUFRLGNBQWxDSixzREFBQUEsZ0NBQW9DTSxJQUFJO29DQUN4RGYsVUFBVTttQ0FKTG5EOzs7Ozs0QkFPWCxFQUFFLE9BQU9VLEdBQUc7Z0NBQ1YscUJBQU8sOERBQUNrQjtvQ0FBZ0JDLFdBQVU7OENBQXVCRTttQ0FBeEMvQjs7Ozs7NEJBQ25CO3dCQUNGO3dCQUNBLE9BQU8rQixLQUFLMUIsSUFBSSxtQkFBSyw4REFBQ3VCOzRCQUFnQkMsV0FBVTtzQ0FBdUJQLGlCQUFpQlM7MkJBQXpEL0I7Ozs7bUNBQXdFO29CQUN6Rzs7Ozs7O1lBR047UUFDRjtRQUVBLHFCQUFPLDhEQUFDNEI7WUFBSUMsV0FBVTtzQkFBdUJQLGlCQUFpQjVCOzs7Ozs7SUFDaEU7SUFFQSxjQUFjO0lBQ2QsTUFBTXlFLGlCQUFpQixDQUFDQztRQUN0QixJQUFJLENBQUNBLGFBQWEsT0FBTztRQUN6QixNQUFNQyxVQUFVLENBQUNELGNBQWMsVUFBUyxFQUFHRSxPQUFPLENBQUM7UUFDbkQsT0FBTyxHQUFXLE9BQVJELFNBQVE7SUFDcEI7SUFFQSxNQUFNRSw0QkFBNEI7UUFDaEMscUJBQXFCO1FBQ3JCLE1BQU1DLFlBQVl0RixRQUFRdUYsY0FBYyxHQUNuQyxTQUF1RCxPQUE5QyxDQUFDdkYsUUFBUXVGLGNBQWMsR0FBRyxPQUFNLEVBQUdILE9BQU8sQ0FBQyxJQUFHLFVBQ3ZELFVBQXlDLE9BQS9CcEYsUUFBUXdGLGlCQUFpQixJQUFJLEdBQUUsZUFDekMsU0FBaUMsT0FBeEJ4RixRQUFReUYsVUFBVSxJQUFJLEdBQUUsZUFDakMsVUFBK0osT0FBckp6RixRQUFRMEYsb0JBQW9CLElBQUkxRixRQUFRd0YsaUJBQWlCLEdBQUcsQ0FBQ3hGLFFBQVF3RixpQkFBaUIsR0FBSXhGLENBQUFBLFFBQVEwRixvQkFBb0IsR0FBRyxVQUFTLENBQUMsRUFBR04sT0FBTyxDQUFDLEtBQUssR0FBRSxpQkFDL0osU0FBa0ksT0FBekhwRixRQUFRMkYsYUFBYSxJQUFJM0YsUUFBUXlGLFVBQVUsR0FBRyxDQUFDekYsUUFBUXlGLFVBQVUsR0FBSXpGLENBQUFBLFFBQVEyRixhQUFhLEdBQUcsVUFBUyxDQUFDLEVBQUdQLE9BQU8sQ0FBQyxLQUFLLEdBQUUsZUFDbEk7UUFFTCxxQkFDRSw4REFBQzFDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDakQsZ0dBQUlBO29CQUFDaUQsV0FBVTs7Ozs7OzhCQUNoQiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ1oyQzs7Ozs7Ozs7Ozs7O0lBSVQ7SUFFQSxXQUFXO0lBQ1gsTUFBTU0sd0JBQXdCO1FBQzVCLElBQUksQ0FBQ3ZGLGVBQWUsQ0FBQ0wsUUFBUXVGLGNBQWMsRUFBRSxPQUFPO1FBRXBELHFCQUNFLDhEQUFDN0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ2M7O2dDQUFLO2dDQUFNd0IsZUFBZWpGLFFBQVF1RixjQUFjOzs7Ozs7O3NDQUNqRCw4REFBQzlCOztnQ0FBSztnQ0FBT3dCLGVBQWVqRixRQUFRNkYsYUFBYTs7Ozs7Ozs7Ozs7Ozs4QkFFbkQsOERBQUNuRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNjOztnQ0FBSztnQ0FBT3pELFFBQVF3RixpQkFBaUI7Z0NBQUM7Z0NBQVVQLGVBQWVqRixRQUFRMEYsb0JBQW9CO2dDQUFFOzs7Ozs7O3NDQUM5Riw4REFBQ2pDOztnQ0FBSztnQ0FBS3pELFFBQVF5RixVQUFVO2dDQUFDO2dDQUFVUixlQUFlakYsUUFBUTJGLGFBQWE7Z0NBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJdEY7SUFFQSxxQkFDRSw4REFBQ2pEO1FBQUlDLFdBQVcsY0FBdUQsT0FBekN4QyxTQUFTLGdCQUFnQjs7WUFDcEQsQ0FBQ0Esd0JBQ0EsOERBQUN1QztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVyx1RUFFZixPQURDckMsU0FBUyxrQkFBa0I7c0NBRTFCQSx1QkFDQyw4REFBQ2IsZ0dBQU1BO2dDQUFDa0QsV0FBVTs7Ozs7cURBRWxCLDhEQUFDcEQsZ0dBQUdBO2dDQUFDb0QsV0FBVTs7Ozs7Ozs7Ozs7d0JBSWxCMUMsZ0JBQWdCQyxXQUFXQSxRQUFRMkIsTUFBTSxLQUFLLDJCQUM3Qyw4REFBQ2E7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNoRCxpRUFBaUJBO2dDQUFDTyxTQUFTQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdEMsOERBQUN3QztnQkFBSUMsV0FBVTs7b0JBRVosQ0FBQ3hDLFVBQVVFLDZCQUNWLDhEQUFDcUM7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDYztnQ0FBS2QsV0FBVTswQ0FDYjNDLFFBQVE4RixLQUFLLElBQUk7Ozs7Ozs0QkFHbkJUOzs7Ozs7O2tDQUdMLDhEQUFDM0M7d0JBQUlDLFdBQVcsd0JBTWpCLE9BTEN4QyxTQUNJLDJCQUNBRyxTQUNBLDZIQUNBOzs0QkFHREwsZ0JBQWdCQyxXQUFXQSxRQUFRMkIsTUFBTSxLQUFLLGdDQUM3Qyw4REFBQ2E7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOzs7OzswREFDRCw4REFBQ0E7Ozs7OzBEQUNELDhEQUFDQTs7Ozs7MERBQ0QsOERBQUNBOzs7OzswREFDRCw4REFBQ0E7Ozs7OzBEQUNELDhEQUFDQTs7Ozs7Ozs7Ozs7a0RBRUgsOERBQUNlO3dDQUFLZCxXQUFVO2tEQUNiekMsUUFBUWdCLFFBQVEsR0FBRyxRQUF5QixPQUFqQmhCLFFBQVFnQixRQUFRLEVBQUMsU0FBTzs7Ozs7Ozs7Ozs7OzRCQUt6RFosU0FBU29ELGtCQUFrQjFELFFBQVFRLE9BQU8sSUFDMUNILGNBQWM2RCx1QkFBdUJsRSxRQUFRUSxPQUFPLGtCQUNwRCw4REFBQ2tDO2dDQUFJQyxXQUFVOzBDQUF1QlAsaUJBQWlCcEMsUUFBUVEsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBSTFFTCx3QkFDQyw4REFBQ3VDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDbkQsZ0dBQUlBO29CQUFDbUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLMUI7S0F4VGdCNUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxjaGF0XFxjb21wb25lbnRzXFxNZXNzYWdlSXRlbS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQm90LCBVc2VyLCBXcmVuY2gsIEluZm8gfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ0AvbGliL2RhdGFiYXNlJztcbmltcG9ydCB7IEFJU3RhdGUgfSBmcm9tICcuLi90eXBlcyc7XG5pbXBvcnQgeyBBSVN0YXR1c0luZGljYXRvciB9IGZyb20gJy4vQUlTdGF0dXNJbmRpY2F0b3InO1xuaW1wb3J0IHsgVG9vbENhbGxWaXN1YWxpemF0aW9uIH0gZnJvbSAnLi9Ub29sQ2FsbFZpc3VhbGl6YXRpb24nO1xuaW1wb3J0IHsgVG9vbExvYWRpbmdBbmltYXRpb24gfSBmcm9tICcuL1Rvb2xMb2FkaW5nQW5pbWF0aW9uJztcbmltcG9ydCB7IFRvb2xDb250ZW50RGlzcGxheSwgVG9vbFJlc3VsdFN1bW1hcnkgfSBmcm9tICcuL1Rvb2xDb250ZW50RGlzcGxheSc7XG5cbmludGVyZmFjZSBNZXNzYWdlSXRlbVByb3BzIHtcbiAgbWVzc2FnZTogTWVzc2FnZTtcbiAgc2hvd0FJU3RhdHVzPzogYm9vbGVhbjtcbiAgYWlTdGF0ZT86IEFJU3RhdGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBNZXNzYWdlSXRlbSh7IG1lc3NhZ2UsIHNob3dBSVN0YXR1cywgYWlTdGF0ZSB9OiBNZXNzYWdlSXRlbVByb3BzKSB7XG4gIGNvbnN0IGlzVXNlciA9IG1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInO1xuICBjb25zdCBpc0Fzc2lzdGFudCA9IG1lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCc7XG4gIGNvbnN0IGlzVG9vbCA9IG1lc3NhZ2Uucm9sZSA9PT0gJ3Rvb2wnO1xuXG4gIC8vIOino+aekOW3peWFt+iwg+eUqOWGheWuue+8jOWIm+W7uuatpemqpOaVsOaNrlxuICBjb25zdCBwYXJzZVRvb2xDYWxsU3RlcHMgPSAoY29udGVudDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3Qgc3RlcHM6IGFueVtdID0gW107XG5cbiAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKvlt6XlhbfosIPnlKjkv6Hmga9cbiAgICBpZiAoY29udGVudC5pbmNsdWRlcygn6LCD55So5bel5YW3OicpICYmIGNvbnRlbnQuaW5jbHVkZXMoJ+WPguaVsDonKSAmJiBjb250ZW50LmluY2x1ZGVzKCfnu5Pmnpw6JykpIHtcbiAgICAgIGNvbnN0IHRvb2xDYWxsTWF0Y2hlcyA9IGNvbnRlbnQubWF0Y2goL+iwg+eUqOW3peWFtzpcXHMqKFteXFxuXSspXFxu5Y+C5pWwOlxccyooW15dKj8pXFxuXFxu57uT5p6cOlxccyooW15dKj8pKD89XFxuXFxufCQpL2cpO1xuXG4gICAgICBpZiAodG9vbENhbGxNYXRjaGVzKSB7XG4gICAgICAgIHRvb2xDYWxsTWF0Y2hlcy5mb3JFYWNoKChtYXRjaCwgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zdCB0b29sTmFtZU1hdGNoID0gbWF0Y2gubWF0Y2goL+iwg+eUqOW3peWFtzpcXHMqKFteXFxuXSspLyk7XG4gICAgICAgICAgY29uc3QgcGFyYW1zTWF0Y2ggPSBtYXRjaC5tYXRjaCgv5Y+C5pWwOlxccyooW15dKj8pXFxuXFxu57uT5p6cOi8pO1xuICAgICAgICAgIGNvbnN0IHJlc3VsdE1hdGNoID0gbWF0Y2gubWF0Y2goL+e7k+aenDpcXHMqKFteXSo/KSg/PVxcblxcbnwkKS8pO1xuXG4gICAgICAgICAgaWYgKHRvb2xOYW1lTWF0Y2gpIHtcbiAgICAgICAgICAgIGNvbnN0IHRvb2xOYW1lID0gdG9vbE5hbWVNYXRjaFsxXS50cmltKCk7XG4gICAgICAgICAgICBsZXQgcGFyYW1zID0ge307XG4gICAgICAgICAgICBsZXQgcmVzdWx0ID0gJyc7XG5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGlmIChwYXJhbXNNYXRjaCkge1xuICAgICAgICAgICAgICAgIHBhcmFtcyA9IEpTT04ucGFyc2UocGFyYW1zTWF0Y2hbMV0udHJpbSgpKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICBwYXJhbXMgPSB7IHJhdzogcGFyYW1zTWF0Y2g/LlsxXT8udHJpbSgpIHx8ICcnIH07XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChyZXN1bHRNYXRjaCkge1xuICAgICAgICAgICAgICByZXN1bHQgPSByZXN1bHRNYXRjaFsxXS50cmltKCk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOa3u+WKoOaJp+ihjOatpemqpFxuICAgICAgICAgICAgc3RlcHMucHVzaCh7XG4gICAgICAgICAgICAgIGlkOiBgZXhlY3V0aW9uLSR7aW5kZXh9YCxcbiAgICAgICAgICAgICAgdHlwZTogJ2V4ZWN1dGlvbicsXG4gICAgICAgICAgICAgIHRvb2xOYW1lLFxuICAgICAgICAgICAgICBzdGF0dXM6ICdzdWNjZXNzJyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICBpbnB1dDogcGFyYW1zXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8g5re75Yqg57uT5p6c5q2l6aqkXG4gICAgICAgICAgICBzdGVwcy5wdXNoKHtcbiAgICAgICAgICAgICAgaWQ6IGByZXN1bHQtJHtpbmRleH1gLFxuICAgICAgICAgICAgICB0eXBlOiAncmVzdWx0JyxcbiAgICAgICAgICAgICAgdG9vbE5hbWUsXG4gICAgICAgICAgICAgIHN0YXR1czogcmVzdWx0LmluY2x1ZGVzKCfplJnor68nKSB8fCByZXN1bHQuaW5jbHVkZXMoJ+Wksei0pScpID8gJ2Vycm9yJyA6ICdzdWNjZXNzJyxcbiAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICBvdXRwdXQ6IHJlc3VsdCxcbiAgICAgICAgICAgICAgZXJyb3I6IHJlc3VsdC5pbmNsdWRlcygn6ZSZ6K+vJykgfHwgcmVzdWx0LmluY2x1ZGVzKCflpLHotKUnKSA/IHJlc3VsdCA6IHVuZGVmaW5lZFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gc3RlcHM7XG4gIH07XG5cbiAgLy8g5qOA5rWL5bm25riy5p+T5Zu+54mHVVJMXG4gIGNvbnN0IHJlbmRlckltYWdlSWZVcmwgPSAodGV4dDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VVcmxSZWdleCA9IC8oaHR0cHM/OlxcL1xcL1teXFxzXStcXC4oanBnfGpwZWd8cG5nfGdpZnx3ZWJwfHN2ZykpL2dpO1xuICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKGltYWdlVXJsUmVnZXgpO1xuICAgIFxuICAgIGlmIChtYXRjaGVzKSB7XG4gICAgICBjb25zdCBwYXJ0cyA9IHRleHQuc3BsaXQoaW1hZ2VVcmxSZWdleCk7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIHtwYXJ0cy5tYXAoKHBhcnQsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICBpZiAoaW1hZ2VVcmxSZWdleC50ZXN0KHBhcnQpKSB7XG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8aW1nIFxuICAgICAgICAgICAgICAgICAgICBzcmM9e3BhcnR9IFxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCLlt6Xlhbfov5Tlm57nmoTlm77niYdcIiBcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctZnVsbCBoLWF1dG8gcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuZGlzcGxheSA9ICdub25lJztcbiAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nPy5jbGFzc0xpc3QucmVtb3ZlKCdoaWRkZW4nKTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiB0ZXh0LXNtIHRleHQtZ3JheS01MDAgaXRhbGljXCI+XG4gICAgICAgICAgICAgICAgICAgIOWbvueJh+WKoOi9veWksei0pToge3BhcnR9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBwYXJ0ID8gPHNwYW4ga2V5PXtpbmRleH0+e3BhcnR9PC9zcGFuPiA6IG51bGw7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIDxzcGFuPnt0ZXh0fTwvc3Bhbj47XG4gIH07XG5cbiAgLy8g5aSE55CG5bel5YW36LCD55So57uT5p6c55qE5pi+56S6XG4gIGNvbnN0IHJlbmRlclRvb2xDb250ZW50ID0gKGNvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b29sRGF0YSA9IEpTT04ucGFyc2UoY29udGVudCk7XG4gICAgICBpZiAodG9vbERhdGEudG9vbF9uYW1lICYmIHRvb2xEYXRhLnJlc3VsdCkge1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxUb29sUmVzdWx0U3VtbWFyeVxuICAgICAgICAgICAgcmVzdWx0PXt0b29sRGF0YS5yZXN1bHR9XG4gICAgICAgICAgICBpc1N1Y2Nlc3M9eyFjb250ZW50LmluY2x1ZGVzKCfplJnor68nKSAmJiAhY29udGVudC5pbmNsdWRlcygn5aSx6LSlJyl9XG4gICAgICAgICAgICBleGVjdXRpb25UaW1lPXt0b29sRGF0YS5leGVjdXRpb25fdGltZX1cbiAgICAgICAgICAvPlxuICAgICAgICApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIC8vIOWmguaenOS4jeaYr0pTT07moLzlvI/vvIzmo4Dmn6XmmK/lkKbmmK/nroDljZXnmoTlt6Xlhbfnu5PmnpxcbiAgICAgIGlmIChjb250ZW50LmluY2x1ZGVzKCflt6XlhbfmiafooYwnKSB8fCBjb250ZW50LmluY2x1ZGVzKCfosIPnlKjlt6XlhbcnKSkge1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxUb29sQ29udGVudERpc3BsYXlcbiAgICAgICAgICAgIGNvbnRlbnQ9e2NvbnRlbnR9XG4gICAgICAgICAgICB0eXBlPVwib3V0cHV0XCJcbiAgICAgICAgICAgIHRpdGxlPVwi5bel5YW35omn6KGM57uT5p6cXCJcbiAgICAgICAgICAgIG1heExpbmVzPXs2fVxuICAgICAgICAgIC8+XG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLXdyYXBcIj57cmVuZGVySW1hZ2VJZlVybChjb250ZW50KX08L2Rpdj47XG4gIH07XG5cbiAgLy8g5aSE55CG5Yqp5omL5raI5oGv5Lit55qE5bel5YW36LCD55SoXG4gIGNvbnN0IHJlbmRlckFzc2lzdGFudENvbnRlbnQgPSAoY29udGVudDogc3RyaW5nKSA9PiB7XG4gICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCr5bel5YW36LCD55So5L+h5oGvXG4gICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJ+iwg+eUqOW3peWFtzonKSB8fCAoY29udGVudC5pbmNsdWRlcygnYGBganNvbicpICYmIGNvbnRlbnQuaW5jbHVkZXMoJ3Rvb2xfY2FsbHMnKSkpIHtcbiAgICAgIGNvbnN0IHRvb2xTdGVwcyA9IHBhcnNlVG9vbENhbGxTdGVwcyhjb250ZW50KTtcblxuICAgICAgaWYgKHRvb2xTdGVwcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIC8vIOWIhuemu+W3peWFt+iwg+eUqOmDqOWIhuWSjOaZrumAmuaWh+acrOmDqOWIhlxuICAgICAgICBjb25zdCB0b29sQ2FsbFBhdHRlcm4gPSAv6LCD55So5bel5YW3OlteXSo/KD89XFxuXFxuKD8h5Y+C5pWwOnznu5Pmnpw6KXwkKS9nO1xuICAgICAgICBjb25zdCB0ZXh0UGFydHMgPSBjb250ZW50LnNwbGl0KHRvb2xDYWxsUGF0dGVybikuZmlsdGVyKHBhcnQgPT4gcGFydC50cmltKCkpO1xuXG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgIHsvKiDlt6XlhbfosIPnlKjlj6/op4bljJYgKi99XG4gICAgICAgICAgICA8VG9vbENhbGxWaXN1YWxpemF0aW9uXG4gICAgICAgICAgICAgIHN0ZXBzPXt0b29sU3RlcHN9XG4gICAgICAgICAgICAgIGlzTG9hZGluZz17ZmFsc2V9XG4gICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICB7Lyog5YW25LuW5paH5pys5YaF5a65ICovfVxuICAgICAgICAgICAge3RleHRQYXJ0cy5tYXAoKHBhcnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIHBhcnQudHJpbSgpICYmICFwYXJ0LmluY2x1ZGVzKCfosIPnlKjlt6Xlhbc6JykgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ3aGl0ZXNwYWNlLXByZS13cmFwXCI+XG4gICAgICAgICAgICAgICAgICB7cmVuZGVySW1hZ2VJZlVybChwYXJ0LnRyaW0oKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBudWxsXG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgLy8g5aSE55CG5pen5qC85byP55qESlNPTuW3peWFt+iwg+eUqFxuICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJ2BgYGpzb24nKSAmJiBjb250ZW50LmluY2x1ZGVzKCd0b29sX2NhbGxzJykpIHtcbiAgICAgICAgY29uc3QgcGFydHMgPSBjb250ZW50LnNwbGl0KCdgYGAnKTtcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAge3BhcnRzLm1hcCgocGFydCwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHBhcnQuc3RhcnRzV2l0aCgnanNvbicpICYmIHBhcnQuaW5jbHVkZXMoJ3Rvb2xfY2FsbHMnKSkge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBqc29uQ29udGVudCA9IHBhcnQucmVwbGFjZSgnanNvblxcbicsICcnKS50cmltKCk7XG4gICAgICAgICAgICAgICAgICBjb25zdCB0b29sQ2FsbCA9IEpTT04ucGFyc2UoanNvbkNvbnRlbnQpO1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPFRvb2xDb250ZW50RGlzcGxheVxuICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgY29udGVudD17dG9vbENhbGwudG9vbF9jYWxscz8uWzBdPy5mdW5jdGlvbj8uYXJndW1lbnRzIHx8IHt9fVxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJpbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e2DosIPnlKjlt6Xlhbc6ICR7dG9vbENhbGwudG9vbF9jYWxscz8uWzBdPy5mdW5jdGlvbj8ubmFtZX1gfVxuICAgICAgICAgICAgICAgICAgICAgIG1heExpbmVzPXs0fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ3aGl0ZXNwYWNlLXByZS13cmFwXCI+e3BhcnR9PC9kaXY+O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICByZXR1cm4gcGFydC50cmltKCkgPyA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cIndoaXRlc3BhY2UtcHJlLXdyYXBcIj57cmVuZGVySW1hZ2VJZlVybChwYXJ0KX08L2Rpdj4gOiBudWxsO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcFwiPntyZW5kZXJJbWFnZUlmVXJsKGNvbnRlbnQpfTwvZGl2PjtcbiAgfTtcblxuICAvLyDmoLzlvI/ljJbml7bpl7TvvIjmr6vnp5Lovaznp5LvvIlcbiAgY29uc3QgZm9ybWF0RHVyYXRpb24gPSAobmFub3NlY29uZHM/OiBudW1iZXIpID0+IHtcbiAgICBpZiAoIW5hbm9zZWNvbmRzKSByZXR1cm4gbnVsbDtcbiAgICBjb25zdCBzZWNvbmRzID0gKG5hbm9zZWNvbmRzIC8gMTAwMDAwMDAwMCkudG9GaXhlZCgyKTtcbiAgICByZXR1cm4gYCR7c2Vjb25kc31zYDtcbiAgfTtcblxuICBjb25zdCByZW5kZXJHZW5lcmF0aW9uU3RhdHNJY29uID0gKCkgPT4ge1xuICAgIC8vIOagueaNruaYr+WQpuaciee7n+iuoeaVsOaNruaYvuekuuS4jeWQjOeahOaCrOa1ruWGheWuuVxuICAgIGNvbnN0IHN0YXRzVGV4dCA9IG1lc3NhZ2UudG90YWxfZHVyYXRpb24gXG4gICAgICAgPyBg55Sf5oiQ5pe26Ze0OiAkeyhtZXNzYWdlLnRvdGFsX2R1cmF0aW9uIC8gMTAwMDAwMCkudG9GaXhlZCgyKX1tc1xcbmAgK1xuICAgICAgICAgYOaPkOekuuivjeWkhOeQhjogJHttZXNzYWdlLnByb21wdF9ldmFsX2NvdW50IHx8IDB9IHRva2Vuc1xcbmAgK1xuICAgICAgICAgYOeUn+aIkOWGheWuuTogJHttZXNzYWdlLmV2YWxfY291bnQgfHwgMH0gdG9rZW5zXFxuYCArXG4gICAgICAgICBg5o+Q56S66K+N6YCf5bqmOiAke21lc3NhZ2UucHJvbXB0X2V2YWxfZHVyYXRpb24gJiYgbWVzc2FnZS5wcm9tcHRfZXZhbF9jb3VudCA/IChtZXNzYWdlLnByb21wdF9ldmFsX2NvdW50IC8gKG1lc3NhZ2UucHJvbXB0X2V2YWxfZHVyYXRpb24gLyAxMDAwMDAwMDAwKSkudG9GaXhlZCgxKSA6IDB9IHRva2Vucy9zXFxuYCArXG4gICAgICAgICBg55Sf5oiQ6YCf5bqmOiAke21lc3NhZ2UuZXZhbF9kdXJhdGlvbiAmJiBtZXNzYWdlLmV2YWxfY291bnQgPyAobWVzc2FnZS5ldmFsX2NvdW50IC8gKG1lc3NhZ2UuZXZhbF9kdXJhdGlvbiAvIDEwMDAwMDAwMDApKS50b0ZpeGVkKDEpIDogMH0gdG9rZW5zL3NgXG4gICAgICAgOiAn5q2j5Zyo55Sf5oiQ5Lit77yM57uf6K6h5L+h5oGv5bCG5Zyo5a6M5oiQ5ZCO5pi+56S6Li4uJztcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgIDxJbmZvIGNsYXNzTmFtZT1cInctMyBoLTMgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTMwMCBjdXJzb3ItaGVscFwiIC8+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0wIGJvdHRvbS01IGJnLWdyYXktODAwIHRleHQtd2hpdGUgdGV4dC14cyByb3VuZGVkIHB4LTIgcHktMSB3aGl0ZXNwYWNlLXByZS1saW5lIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwIHotMTAgbWluLXctbWF4XCI+XG4gICAgICAgICAge3N0YXRzVGV4dH1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIOa4suafk+eUn+aIkOe7n+iuoeS/oeaBr1xuICBjb25zdCByZW5kZXJHZW5lcmF0aW9uU3RhdHMgPSAoKSA9PiB7XG4gICAgaWYgKCFpc0Fzc2lzdGFudCB8fCAhbWVzc2FnZS50b3RhbF9kdXJhdGlvbikgcmV0dXJuIG51bGw7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDAgc3BhY2UteS0xXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTRcIj5cbiAgICAgICAgICA8c3Bhbj7mgLvml7bplb86IHtmb3JtYXREdXJhdGlvbihtZXNzYWdlLnRvdGFsX2R1cmF0aW9uKX08L3NwYW4+XG4gICAgICAgICAgPHNwYW4+5Yqg6L295pe26ZW/OiB7Zm9ybWF0RHVyYXRpb24obWVzc2FnZS5sb2FkX2R1cmF0aW9uKX08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00XCI+XG4gICAgICAgICAgPHNwYW4+5o+Q56S66K+E5LywOiB7bWVzc2FnZS5wcm9tcHRfZXZhbF9jb3VudH0gdG9rZW5zICh7Zm9ybWF0RHVyYXRpb24obWVzc2FnZS5wcm9tcHRfZXZhbF9kdXJhdGlvbil9KTwvc3Bhbj5cbiAgICAgICAgICA8c3Bhbj7nlJ/miJA6IHttZXNzYWdlLmV2YWxfY291bnR9IHRva2VucyAoe2Zvcm1hdER1cmF0aW9uKG1lc3NhZ2UuZXZhbF9kdXJhdGlvbil9KTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGdhcC0zICR7aXNVc2VyID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfT5cbiAgICAgIHshaXNVc2VyICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0yXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctOCBoLTggcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZsZXgtc2hyaW5rLTAgJHtcbiAgICAgICAgICAgICAgaXNUb29sID8gJ2JnLW9yYW5nZS02MDAnIDogJ2JnLWJsdWUtNjAwJ1xuICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICB7aXNUb29sID8gKFxuICAgICAgICAgICAgICAgIDxXcmVuY2ggY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8Qm90IGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBBSeeKtuaAgeaMh+ekuuWZqOaYvuekuuWcqOWktOWDj+WPs+S+pyAqL31cbiAgICAgICAgICAgIHtzaG93QUlTdGF0dXMgJiYgYWlTdGF0ZSAmJiBhaVN0YXRlLnN0YXR1cyA9PT0gJ2xvYWRpbmcnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XG4gICAgICAgICAgICAgICAgPEFJU3RhdHVzSW5kaWNhdG9yIGFpU3RhdGU9e2FpU3RhdGV9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgICAgXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWF4LXctWzcwJV1cIj5cbiAgICAgICAgey8qIOaooeWei+WQjeensOWSjOe7n+iuoeWbvuagh+aYvuekuuWcqOa2iOaBr+awlOazoeS4iuaWuSAqL31cbiAgICAgICAgeyFpc1VzZXIgJiYgaXNBc3Npc3RhbnQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMVwiPlxuICAgICAgICAgICAgey8qIOaooeWei+WQjeensOaYvuekuu+8jOWmguaenOayoeacieWImeaYvuekuuWKoOi9veS4rSAqL31cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAge21lc3NhZ2UubW9kZWwgfHwgJ+WKoOi9veS4rS4uLid9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICB7Lyog57uf6K6h5L+h5oGv5Zu+5qCH5aeL57uI5pi+56S677yM5L2G5oKs5rWu5YaF5a655qC55o2u5pWw5o2u5Y+v55So5oCn5Yaz5a6aICovfVxuICAgICAgICAgICAge3JlbmRlckdlbmVyYXRpb25TdGF0c0ljb24oKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Byb3VuZGVkLWxnIHB4LTQgcHktMiAke1xuICAgICAgICBpc1VzZXJcbiAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgIDogaXNUb29sXG4gICAgICAgICAgPyAnYmctb3JhbmdlLTEwMCBkYXJrOmJnLW9yYW5nZS05MDAvMjAgdGV4dC1vcmFuZ2UtODAwIGRhcms6dGV4dC1vcmFuZ2UtMjAwIGJvcmRlciBib3JkZXItb3JhbmdlLTMwMCBkYXJrOmJvcmRlci1vcmFuZ2UtNzAwJ1xuICAgICAgICAgIDogJ2JnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSdcbiAgICAgIH1gfT5cbiAgICAgICAgICB7Lyog5bel5YW36LCD55SobG9hZGluZ+eKtuaAgeaYvuekuuWcqOa2iOaBr+awlOazoeWGhSAqL31cbiAgICAgICAgICB7c2hvd0FJU3RhdHVzICYmIGFpU3RhdGUgJiYgYWlTdGF0ZS5zdGF0dXMgPT09ICd0b29sX2NhbGxpbmcnICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItMiBwLTIgYmctZ3JlZW4tNTAgZGFyazpiZy1ncmVlbi05MDAvMjAgcm91bmRlZCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyLXNtYWxsXCI+XG4gICAgICAgICAgICAgICAgPGRpdj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0zMDBcIj5cbiAgICAgICAgICAgICAgICB7YWlTdGF0ZS50b29sTmFtZSA/IGDmraPlnKjosIPnlKggJHthaVN0YXRlLnRvb2xOYW1lfS4uLmAgOiAn5q2j5Zyo6LCD55So5bel5YW3Li4uJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICBcbiAgICAgICAgICB7aXNUb29sID8gcmVuZGVyVG9vbENvbnRlbnQobWVzc2FnZS5jb250ZW50KSA6IFxuICAgICAgICAgICBpc0Fzc2lzdGFudCA/IHJlbmRlckFzc2lzdGFudENvbnRlbnQobWVzc2FnZS5jb250ZW50KSA6XG4gICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2hpdGVzcGFjZS1wcmUtd3JhcFwiPntyZW5kZXJJbWFnZUlmVXJsKG1lc3NhZ2UuY29udGVudCl9PC9kaXY+fVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICB7aXNVc2VyICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYXktNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkJvdCIsIlVzZXIiLCJXcmVuY2giLCJJbmZvIiwiQUlTdGF0dXNJbmRpY2F0b3IiLCJUb29sQ2FsbFZpc3VhbGl6YXRpb24iLCJUb29sQ29udGVudERpc3BsYXkiLCJUb29sUmVzdWx0U3VtbWFyeSIsIk1lc3NhZ2VJdGVtIiwibWVzc2FnZSIsInNob3dBSVN0YXR1cyIsImFpU3RhdGUiLCJpc1VzZXIiLCJyb2xlIiwiaXNBc3Npc3RhbnQiLCJpc1Rvb2wiLCJwYXJzZVRvb2xDYWxsU3RlcHMiLCJjb250ZW50Iiwic3RlcHMiLCJpbmNsdWRlcyIsInRvb2xDYWxsTWF0Y2hlcyIsIm1hdGNoIiwiZm9yRWFjaCIsImluZGV4IiwidG9vbE5hbWVNYXRjaCIsInBhcmFtc01hdGNoIiwicmVzdWx0TWF0Y2giLCJ0b29sTmFtZSIsInRyaW0iLCJwYXJhbXMiLCJyZXN1bHQiLCJKU09OIiwicGFyc2UiLCJlIiwicmF3IiwicHVzaCIsImlkIiwidHlwZSIsInN0YXR1cyIsInRpbWVzdGFtcCIsIkRhdGUiLCJpbnB1dCIsIm91dHB1dCIsImVycm9yIiwidW5kZWZpbmVkIiwicmVuZGVySW1hZ2VJZlVybCIsInRleHQiLCJpbWFnZVVybFJlZ2V4IiwibWF0Y2hlcyIsInBhcnRzIiwic3BsaXQiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJwYXJ0IiwidGVzdCIsImltZyIsInNyYyIsImFsdCIsIm9uRXJyb3IiLCJjdXJyZW50VGFyZ2V0Iiwic3R5bGUiLCJkaXNwbGF5IiwibmV4dEVsZW1lbnRTaWJsaW5nIiwiY2xhc3NMaXN0IiwicmVtb3ZlIiwic3BhbiIsInJlbmRlclRvb2xDb250ZW50IiwidG9vbERhdGEiLCJ0b29sX25hbWUiLCJpc1N1Y2Nlc3MiLCJleGVjdXRpb25UaW1lIiwiZXhlY3V0aW9uX3RpbWUiLCJ0aXRsZSIsIm1heExpbmVzIiwicmVuZGVyQXNzaXN0YW50Q29udGVudCIsInRvb2xTdGVwcyIsImxlbmd0aCIsInRvb2xDYWxsUGF0dGVybiIsInRleHRQYXJ0cyIsImZpbHRlciIsImlzTG9hZGluZyIsInN0YXJ0c1dpdGgiLCJ0b29sQ2FsbCIsImpzb25Db250ZW50IiwicmVwbGFjZSIsInRvb2xfY2FsbHMiLCJmdW5jdGlvbiIsImFyZ3VtZW50cyIsIm5hbWUiLCJmb3JtYXREdXJhdGlvbiIsIm5hbm9zZWNvbmRzIiwic2Vjb25kcyIsInRvRml4ZWQiLCJyZW5kZXJHZW5lcmF0aW9uU3RhdHNJY29uIiwic3RhdHNUZXh0IiwidG90YWxfZHVyYXRpb24iLCJwcm9tcHRfZXZhbF9jb3VudCIsImV2YWxfY291bnQiLCJwcm9tcHRfZXZhbF9kdXJhdGlvbiIsImV2YWxfZHVyYXRpb24iLCJyZW5kZXJHZW5lcmF0aW9uU3RhdHMiLCJsb2FkX2R1cmF0aW9uIiwibW9kZWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/ToolCallVisualization.tsx":
/*!***********************************************************!*\
  !*** ./src/app/chat/components/ToolCallVisualization.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolCallVisualization: () => (/* binding */ ToolCallVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,ChevronDown,ChevronUp,Clock,Cog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ ToolCallVisualization auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ToolCallVisualization(param) {\n    let { steps, isLoading = false, currentToolName } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [expandedSteps, setExpandedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const toggleStepExpansion = (stepId)=>{\n        const newExpanded = new Set(expandedSteps);\n        if (newExpanded.has(stepId)) {\n            newExpanded.delete(stepId);\n        } else {\n            newExpanded.add(stepId);\n        }\n        setExpandedSteps(newExpanded);\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 16\n                }, this);\n            case 'running':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-blue-200 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this);\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-4 h-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'pending':\n                return 'border-gray-200 bg-gray-50';\n            case 'running':\n                return 'border-blue-200 bg-blue-50';\n            case 'success':\n                return 'border-green-200 bg-green-50';\n            case 'error':\n                return 'border-red-200 bg-red-50';\n            default:\n                return 'border-gray-200 bg-gray-50';\n        }\n    };\n    const formatContent = (content)=>{\n        if (typeof content === 'string') {\n            return content;\n        }\n        return JSON.stringify(content, null, 2);\n    };\n    const truncateContent = function(content) {\n        let maxLines = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 6;\n        const lines = content.split('\\n');\n        if (lines.length <= maxLines) {\n            return content;\n        }\n        return lines.slice(0, maxLines).join('\\n') + '\\n...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-sm\",\n                                children: [\n                                    \"工具调用 \",\n                                    isLoading && currentToolName && \"(正在执行: \".concat(currentToolName, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            steps.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full\",\n                                children: [\n                                    steps.length,\n                                    \" 步骤\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-4 h-4 text-gray-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-h-96 overflow-y-auto\",\n                children: [\n                    isLoading && currentToolName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-5 h-5 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 border-2 border-blue-200 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-sm text-blue-800 dark:text-blue-200\",\n                                            children: [\n                                                \"正在执行: \",\n                                                currentToolName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-600 dark:text-blue-300\",\n                                            children: \"请稍候...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this),\n                    steps.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200 dark:divide-gray-600\",\n                        children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-3 \".concat(getStatusColor(step.status), \" dark:bg-gray-800 dark:border-gray-600 cursor-pointer\"),\n                                    onClick: ()=>toggleStepExpansion(step.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        getStatusIcon(step.status),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: [\n                                                                        step.type === 'execution' ? '执行工具' : '工具结果',\n                                                                        \": \",\n                                                                        step.toolName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                step.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"耗时: \",\n                                                                        step.duration,\n                                                                        \"ms\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                                    lineNumber: 157,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (step.input || step.output || step.error) && (expandedSteps.has(step.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 27\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 27\n                                                }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 21\n                                        }, this),\n                                        expandedSteps.has(step.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3 space-y-2\",\n                                            children: [\n                                                step.input && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\",\n                                                            children: \"输入参数:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-xs bg-white dark:bg-gray-900 p-2 rounded border overflow-x-auto\",\n                                                            children: formatContent(step.input)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 27\n                                                }, this),\n                                                step.output && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\",\n                                                            children: \"输出结果:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs bg-white dark:bg-gray-900 p-2 rounded border max-h-32 overflow-y-auto\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"whitespace-pre-wrap\",\n                                                                children: truncateContent(formatContent(step.output))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 27\n                                                }, this),\n                                                step.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs font-medium text-red-600 dark:text-red-400 mb-1\",\n                                                            children: \"错误信息:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-2 rounded border border-red-200 dark:border-red-700\",\n                                                            children: step.error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 19\n                                }, this)\n                            }, step.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this) : !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center text-gray-500 dark:text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_ChevronDown_ChevronUp_Clock_Cog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: \"暂无工具调用记录\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolCallVisualization.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolCallVisualization, \"Z5F07nbNQzH/gY7NAXaWciPld2g=\");\n_c = ToolCallVisualization;\nvar _c;\n$RefreshReg$(_c, \"ToolCallVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolCallVisualization.tsx\n"));

/***/ })

});