/**
 * MCP工具集成模块 - 浏览器版本
 * 通过API路由与服务器端MCP功能通信，避免在浏览器中使用Node.js特定模块
 */
/**
 * 浏览器端MCP客户端管理器
 * 通过HTTP API与服务器端MCP功能通信
 */
export class McpClientManager {
    constructor() {
        this.isConnected = false;
        this.availableTools = [];
        // 浏览器环境下不需要直接连接MCP服务器
    }
    /**
     * 初始化连接（浏览器环境下为模拟）
     */
    async connect() {
        try {
            // 通过API检查MCP服务器状态
            const response = await fetch('/api/mcp/status');
            if (response.ok) {
                this.isConnected = true;
                await this.refreshTools();
                return true;
            }
            return false;
        }
        catch (error) {
            console.warn('MCP服务器连接失败，将使用本地工具:', error);
            return false;
        }
    }
    /**
     * 断开连接
     */
    async disconnect() {
        this.isConnected = false;
        this.availableTools = [];
    }
    /**
     * 检查是否已连接
     */
    isConnectedToServer() {
        return this.isConnected;
    }
    /**
     * 获取连接状态（兼容性方法）
     */
    getConnectionStatus() {
        return this.isConnected;
    }
    /**
     * 获取可用工具列表
     */
    getAvailableTools() {
        return this.availableTools;
    }
    /**
     * 刷新工具列表
     */
    async refreshTools() {
        await this.refreshAvailableTools();
    }
    /**
     * 刷新可用工具列表（兼容性方法）
     */
    async refreshAvailableTools() {
        try {
            const response = await fetch('/api/mcp/tools');
            if (response.ok) {
                const data = await response.json();
                this.availableTools = data.tools || [];
                return this.availableTools;
            }
        }
        catch (error) {
            console.error('刷新MCP工具列表失败:', error);
        }
        return [];
    }
    /**
     * 转换为Ollama工具格式
     */
    convertToOllamaTools() {
        return this.availableTools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description || '',
                parameters: tool.inputSchema || {
                    type: 'object',
                    properties: {},
                    required: []
                }
            }
        }));
    }
    /**
     * 检查工具是否可用
     */
    isToolAvailable(toolName) {
        return this.availableTools.some(tool => tool.name === toolName);
    }
    /**
     * 执行工具调用（兼容性方法）
     */
    async executeToolCall(toolCall) {
        return await this.callTool(toolCall);
    }
    /**
     * 执行工具调用
     */
    async callTool(toolCall) {
        try {
            const response = await fetch('/api/mcp/call-tool', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(toolCall),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            return {
                success: true,
                result: result.result,
                content: result.content
            };
        }
        catch (error) {
            console.error('MCP工具调用失败:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }
    /**
     * 批量执行工具调用
     */
    async callTools(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            const result = await this.callTool(toolCall);
            results.push(result);
        }
        return results;
    }
}
// 全局MCP客户端管理器实例
export const mcpClientManager = new McpClientManager();
/**
 * 初始化MCP客户端
 */
export async function initializeMcpClient() {
    try {
        const success = await mcpClientManager.connect();
        if (success) {
            console.log('MCP客户端初始化成功（浏览器模式）');
        }
        else {
            console.warn('MCP客户端初始化失败，将使用本地工具');
        }
        return success;
    }
    catch (error) {
        console.error('MCP客户端初始化出错:', error);
        return false;
    }
}
/**
 * 获取MCP工具列表
 */
export async function getMcpTools() {
    if (!mcpClientManager.isConnectedToServer()) {
        return [];
    }
    return mcpClientManager.getAvailableTools();
}
/**
 * 执行MCP工具调用
 */
export async function executeMcpTool(toolCall) {
    return await mcpClientManager.callTool(toolCall);
}
