import { NextResponse } from 'next/server';
import { dbOperations } from '../../../lib/database';
// 获取所有对话
export async function GET(request) {
    try {
        const conversations = dbOperations.getAllConversations();
        return NextResponse.json({
            success: true,
            conversations
        });
    }
    catch (error) {
        console.error('获取对话列表失败:', error);
        return NextResponse.json({
            error: '获取对话列表失败',
            message: error instanceof Error ? error.message : '未知错误'
        }, { status: 500 });
    }
}
// 创建新对话
export async function POST(request) {
    try {
        const body = await request.json();
        const { title, model } = body;
        // 验证必需参数
        if (!title || !model) {
            return NextResponse.json({ error: '缺少必需参数: title 和 model' }, { status: 400 });
        }
        // 创建新对话
        const conversationId = dbOperations.createConversation({
            title: title.trim(),
            model
        });
        // 获取创建的对话
        const conversation = dbOperations.getConversationById(conversationId);
        return NextResponse.json({
            success: true,
            conversation
        }, { status: 201 });
    }
    catch (error) {
        console.error('创建对话失败:', error);
        return NextResponse.json({
            error: '创建对话失败',
            message: error instanceof Error ? error.message : '未知错误'
        }, { status: 500 });
    }
}
