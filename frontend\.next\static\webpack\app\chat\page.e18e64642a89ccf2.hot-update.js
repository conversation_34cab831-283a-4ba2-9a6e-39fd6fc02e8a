"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/hooks/useToolSettings.ts":
/*!***********************************************!*\
  !*** ./src/app/chat/hooks/useToolSettings.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToolSettings: () => (/* binding */ useToolSettings)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/tools */ \"(app-pages-browser)/./src/lib/tools.ts\");\n/* __next_internal_client_entry_do_not_use__ useToolSettings auto */ \n\nfunction useToolSettings(param) {\n    let { selectedModel, enableTools, selectedTools, onToolsToggle, onSelectedToolsChange } = param;\n    const [showToolSettings, setShowToolSettings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [modelSupportsTools, setModelSupportsTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isCheckingModel, setIsCheckingModel] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [allTools, setAllTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_tools__WEBPACK_IMPORTED_MODULE_1__.availableTools);\n    // 验证模型是否支持工具调用\n    const checkModelToolSupport = async (model)=>{\n        if (!model) return;\n        setIsCheckingModel(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: model,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: 'test'\n                        }\n                    ],\n                    enableTools: true,\n                    testMode: true\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setModelSupportsTools(data.supportsTools);\n            } else {\n                setModelSupportsTools(false);\n            }\n        } catch (error) {\n            setModelSupportsTools(false);\n        } finally{\n            setIsCheckingModel(false);\n        }\n    };\n    // 加载所有可用工具（包括MCP工具）\n    const loadTools = async ()=>{\n        try {\n            const tools = await (0,_lib_tools__WEBPACK_IMPORTED_MODULE_1__.getAllAvailableTools)();\n            setAllTools(tools);\n        } catch (error) {\n            console.error('加载工具失败:', error);\n            setAllTools(_lib_tools__WEBPACK_IMPORTED_MODULE_1__.availableTools);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useToolSettings.useEffect\": ()=>{\n            loadTools();\n        }\n    }[\"useToolSettings.useEffect\"], []);\n    // 当模型改变时检查工具支持\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useToolSettings.useEffect\": ()=>{\n            if (selectedModel) {\n                // 无论工具是否启用，都检查模型支持情况\n                checkModelToolSupport(selectedModel);\n            } else {\n                setModelSupportsTools(null);\n            }\n        }\n    }[\"useToolSettings.useEffect\"], [\n        selectedModel\n    ]); // 移除enableTools依赖，确保模型改变时总是检查\n    // 处理工具开关切换\n    const handleToolsToggle = async ()=>{\n        if (!enableTools) {\n            // 开启工具时，先检查模型支持\n            if (modelSupportsTools === null && selectedModel) {\n                await checkModelToolSupport(selectedModel);\n            }\n            // 如果模型不支持工具调用，阻止启用\n            if (modelSupportsTools === false) {\n                alert(\"模型 \".concat(selectedModel, \" 不支持工具调用功能，请选择其他模型。\"));\n                return;\n            }\n            // 如果还在检查中，等待检查完成\n            if (isCheckingModel) {\n                return;\n            }\n        }\n        onToolsToggle(!enableTools);\n    };\n    // 处理工具选择\n    const handleToolSelection = (toolName)=>{\n        const newSelectedTools = selectedTools.includes(toolName) ? selectedTools.filter((t)=>t !== toolName) : [\n            ...selectedTools,\n            toolName\n        ];\n        onSelectedToolsChange(newSelectedTools);\n    };\n    return {\n        showToolSettings,\n        setShowToolSettings,\n        modelSupportsTools,\n        isCheckingModel,\n        allTools,\n        handleToolsToggle,\n        handleToolSelection\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9ob29rcy91c2VUb29sU2V0dGluZ3MudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztxRUFFNEM7QUFDdUI7QUFXNUQsU0FBU0ksZ0JBQWdCLEtBTVQ7UUFOUyxFQUM5QkMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxxQkFBcUIsRUFDQSxHQU5TO0lBTzlCLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR1gsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDWSxvQkFBb0JDLHNCQUFzQixHQUFHYiwrQ0FBUUEsQ0FBaUI7SUFDN0UsTUFBTSxDQUFDYyxpQkFBaUJDLG1CQUFtQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNnQixVQUFVQyxZQUFZLEdBQUdqQiwrQ0FBUUEsQ0FBU0Usc0RBQWNBO0lBRS9ELGVBQWU7SUFDZixNQUFNZ0Isd0JBQXdCLE9BQU9DO1FBQ25DLElBQUksQ0FBQ0EsT0FBTztRQUVaSixtQkFBbUI7UUFDbkIsSUFBSTtZQUNGLE1BQU1LLFdBQVcsTUFBTUMsTUFBTSxhQUFhO2dCQUN4Q0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CUCxPQUFPQTtvQkFDUFEsVUFBVTt3QkFBQzs0QkFBRUMsTUFBTTs0QkFBUUMsU0FBUzt3QkFBTztxQkFBRTtvQkFDN0N2QixhQUFhO29CQUNid0IsVUFBVTtnQkFDWjtZQUNGO1lBRUEsTUFBTUMsT0FBTyxNQUFNWCxTQUFTWSxJQUFJO1lBQ2hDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJwQixzQkFBc0JrQixLQUFLRyxhQUFhO1lBQzFDLE9BQU87Z0JBQ0xyQixzQkFBc0I7WUFDeEI7UUFDRixFQUFFLE9BQU9zQixPQUFPO1lBQ2R0QixzQkFBc0I7UUFDeEIsU0FBVTtZQUNSRSxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNcUIsWUFBWTtRQUNoQixJQUFJO1lBQ0YsTUFBTUMsUUFBUSxNQUFNbEMsZ0VBQW9CQTtZQUN4Q2MsWUFBWW9CO1FBQ2QsRUFBRSxPQUFPRixPQUFPO1lBQ2RHLFFBQVFILEtBQUssQ0FBQyxXQUFXQTtZQUN6QmxCLFlBQVlmLHNEQUFjQTtRQUM1QjtJQUNGO0lBRUFELGdEQUFTQTtxQ0FBQztZQUNSbUM7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsZUFBZTtJQUNmbkMsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSUksZUFBZTtnQkFDakIscUJBQXFCO2dCQUNyQmEsc0JBQXNCYjtZQUN4QixPQUFPO2dCQUNMUSxzQkFBc0I7WUFDeEI7UUFDRjtvQ0FBRztRQUFDUjtLQUFjLEdBQUcsOEJBQThCO0lBRW5ELFdBQVc7SUFDWCxNQUFNa0Msb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ2pDLGFBQWE7WUFDaEIsZ0JBQWdCO1lBQ2hCLElBQUlNLHVCQUF1QixRQUFRUCxlQUFlO2dCQUNoRCxNQUFNYSxzQkFBc0JiO1lBQzlCO1lBRUEsbUJBQW1CO1lBQ25CLElBQUlPLHVCQUF1QixPQUFPO2dCQUNoQzRCLE1BQU0sTUFBb0IsT0FBZG5DLGVBQWM7Z0JBQzFCO1lBQ0Y7WUFFQSxpQkFBaUI7WUFDakIsSUFBSVMsaUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0Y7UUFDQU4sY0FBYyxDQUFDRjtJQUNqQjtJQUVBLFNBQVM7SUFDVCxNQUFNbUMsc0JBQXNCLENBQUNDO1FBQzNCLE1BQU1DLG1CQUFtQnBDLGNBQWNxQyxRQUFRLENBQUNGLFlBQzVDbkMsY0FBY3NDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTUosWUFDaEM7ZUFBSW5DO1lBQWVtQztTQUFTO1FBQ2hDakMsc0JBQXNCa0M7SUFDeEI7SUFFQSxPQUFPO1FBQ0xqQztRQUNBQztRQUNBQztRQUNBRTtRQUNBRTtRQUNBdUI7UUFDQUU7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcY2hhdFxcaG9va3NcXHVzZVRvb2xTZXR0aW5ncy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhdmFpbGFibGVUb29scywgZ2V0QWxsQXZhaWxhYmxlVG9vbHMgfSBmcm9tICdAL2xpYi90b29scyc7XG5pbXBvcnQgeyBUb29sIH0gZnJvbSAnQC9saWIvb2xsYW1hJztcblxuaW50ZXJmYWNlIFVzZVRvb2xTZXR0aW5nc1Byb3BzIHtcbiAgc2VsZWN0ZWRNb2RlbDogc3RyaW5nO1xuICBlbmFibGVUb29sczogYm9vbGVhbjtcbiAgc2VsZWN0ZWRUb29sczogc3RyaW5nW107XG4gIG9uVG9vbHNUb2dnbGU6IChlbmFibGVkOiBib29sZWFuKSA9PiB2b2lkO1xuICBvblNlbGVjdGVkVG9vbHNDaGFuZ2U6ICh0b29sczogc3RyaW5nW10pID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUb29sU2V0dGluZ3Moe1xuICBzZWxlY3RlZE1vZGVsLFxuICBlbmFibGVUb29scyxcbiAgc2VsZWN0ZWRUb29scyxcbiAgb25Ub29sc1RvZ2dsZSxcbiAgb25TZWxlY3RlZFRvb2xzQ2hhbmdlLFxufTogVXNlVG9vbFNldHRpbmdzUHJvcHMpIHtcbiAgY29uc3QgW3Nob3dUb29sU2V0dGluZ3MsIHNldFNob3dUb29sU2V0dGluZ3NdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbW9kZWxTdXBwb3J0c1Rvb2xzLCBzZXRNb2RlbFN1cHBvcnRzVG9vbHNdID0gdXNlU3RhdGU8Ym9vbGVhbiB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNDaGVja2luZ01vZGVsLCBzZXRJc0NoZWNraW5nTW9kZWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWxsVG9vbHMsIHNldEFsbFRvb2xzXSA9IHVzZVN0YXRlPFRvb2xbXT4oYXZhaWxhYmxlVG9vbHMpO1xuXG4gIC8vIOmqjOivgeaooeWei+aYr+WQpuaUr+aMgeW3peWFt+iwg+eUqFxuICBjb25zdCBjaGVja01vZGVsVG9vbFN1cHBvcnQgPSBhc3luYyAobW9kZWw6IHN0cmluZykgPT4ge1xuICAgIGlmICghbW9kZWwpIHJldHVybjtcbiAgICBcbiAgICBzZXRJc0NoZWNraW5nTW9kZWwodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2hhdCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbW9kZWw6IG1vZGVsLFxuICAgICAgICAgIG1lc3NhZ2VzOiBbeyByb2xlOiAndXNlcicsIGNvbnRlbnQ6ICd0ZXN0JyB9XSxcbiAgICAgICAgICBlbmFibGVUb29sczogdHJ1ZSxcbiAgICAgICAgICB0ZXN0TW9kZTogdHJ1ZSwgLy8g5re75Yqg5rWL6K+V5qih5byP5qCH6K+GXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldE1vZGVsU3VwcG9ydHNUb29scyhkYXRhLnN1cHBvcnRzVG9vbHMgYXMgYm9vbGVhbik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRNb2RlbFN1cHBvcnRzVG9vbHMoZmFsc2UpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXRNb2RlbFN1cHBvcnRzVG9vbHMoZmFsc2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0NoZWNraW5nTW9kZWwoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3miYDmnInlj6/nlKjlt6XlhbfvvIjljIXmi6xNQ1Dlt6XlhbfvvIlcbiAgY29uc3QgbG9hZFRvb2xzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b29scyA9IGF3YWl0IGdldEFsbEF2YWlsYWJsZVRvb2xzKCk7XG4gICAgICBzZXRBbGxUb29scyh0b29scyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veW3peWFt+Wksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRBbGxUb29scyhhdmFpbGFibGVUb29scyk7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFRvb2xzKCk7XG4gIH0sIFtdKTtcblxuICAvLyDlvZPmqKHlnovmlLnlj5jml7bmo4Dmn6Xlt6XlhbfmlK/mjIFcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRNb2RlbCkge1xuICAgICAgLy8g5peg6K665bel5YW35piv5ZCm5ZCv55So77yM6YO95qOA5p+l5qih5Z6L5pSv5oyB5oOF5Ya1XG4gICAgICBjaGVja01vZGVsVG9vbFN1cHBvcnQoc2VsZWN0ZWRNb2RlbCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldE1vZGVsU3VwcG9ydHNUb29scyhudWxsKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZE1vZGVsXSk7IC8vIOenu+mZpGVuYWJsZVRvb2xz5L6d6LWW77yM56Gu5L+d5qih5Z6L5pS55Y+Y5pe25oC75piv5qOA5p+lXG5cbiAgLy8g5aSE55CG5bel5YW35byA5YWz5YiH5o2iXG4gIGNvbnN0IGhhbmRsZVRvb2xzVG9nZ2xlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZW5hYmxlVG9vbHMpIHtcbiAgICAgIC8vIOW8gOWQr+W3peWFt+aXtu+8jOWFiOajgOafpeaooeWei+aUr+aMgVxuICAgICAgaWYgKG1vZGVsU3VwcG9ydHNUb29scyA9PT0gbnVsbCAmJiBzZWxlY3RlZE1vZGVsKSB7XG4gICAgICAgIGF3YWl0IGNoZWNrTW9kZWxUb29sU3VwcG9ydChzZWxlY3RlZE1vZGVsKTtcbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5qih5Z6L5LiN5pSv5oyB5bel5YW36LCD55So77yM6Zi75q2i5ZCv55SoXG4gICAgICBpZiAobW9kZWxTdXBwb3J0c1Rvb2xzID09PSBmYWxzZSkge1xuICAgICAgICBhbGVydChg5qih5Z6LICR7c2VsZWN0ZWRNb2RlbH0g5LiN5pSv5oyB5bel5YW36LCD55So5Yqf6IO977yM6K+36YCJ5oup5YW25LuW5qih5Z6L44CCYCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c6L+Y5Zyo5qOA5p+l5Lit77yM562J5b6F5qOA5p+l5a6M5oiQXG4gICAgICBpZiAoaXNDaGVja2luZ01vZGVsKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG4gICAgb25Ub29sc1RvZ2dsZSghZW5hYmxlVG9vbHMpO1xuICB9O1xuXG4gIC8vIOWkhOeQhuW3peWFt+mAieaLqVxuICBjb25zdCBoYW5kbGVUb29sU2VsZWN0aW9uID0gKHRvb2xOYW1lOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBuZXdTZWxlY3RlZFRvb2xzID0gc2VsZWN0ZWRUb29scy5pbmNsdWRlcyh0b29sTmFtZSlcbiAgICAgID8gc2VsZWN0ZWRUb29scy5maWx0ZXIodCA9PiB0ICE9PSB0b29sTmFtZSlcbiAgICAgIDogWy4uLnNlbGVjdGVkVG9vbHMsIHRvb2xOYW1lXTtcbiAgICBvblNlbGVjdGVkVG9vbHNDaGFuZ2UobmV3U2VsZWN0ZWRUb29scyk7XG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICBzaG93VG9vbFNldHRpbmdzLFxuICAgIHNldFNob3dUb29sU2V0dGluZ3MsXG4gICAgbW9kZWxTdXBwb3J0c1Rvb2xzLFxuICAgIGlzQ2hlY2tpbmdNb2RlbCxcbiAgICBhbGxUb29scyxcbiAgICBoYW5kbGVUb29sc1RvZ2dsZSxcbiAgICBoYW5kbGVUb29sU2VsZWN0aW9uLFxuICB9O1xufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImF2YWlsYWJsZVRvb2xzIiwiZ2V0QWxsQXZhaWxhYmxlVG9vbHMiLCJ1c2VUb29sU2V0dGluZ3MiLCJzZWxlY3RlZE1vZGVsIiwiZW5hYmxlVG9vbHMiLCJzZWxlY3RlZFRvb2xzIiwib25Ub29sc1RvZ2dsZSIsIm9uU2VsZWN0ZWRUb29sc0NoYW5nZSIsInNob3dUb29sU2V0dGluZ3MiLCJzZXRTaG93VG9vbFNldHRpbmdzIiwibW9kZWxTdXBwb3J0c1Rvb2xzIiwic2V0TW9kZWxTdXBwb3J0c1Rvb2xzIiwiaXNDaGVja2luZ01vZGVsIiwic2V0SXNDaGVja2luZ01vZGVsIiwiYWxsVG9vbHMiLCJzZXRBbGxUb29scyIsImNoZWNrTW9kZWxUb29sU3VwcG9ydCIsIm1vZGVsIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1lc3NhZ2VzIiwicm9sZSIsImNvbnRlbnQiLCJ0ZXN0TW9kZSIsImRhdGEiLCJqc29uIiwic3VjY2VzcyIsInN1cHBvcnRzVG9vbHMiLCJlcnJvciIsImxvYWRUb29scyIsInRvb2xzIiwiY29uc29sZSIsImhhbmRsZVRvb2xzVG9nZ2xlIiwiYWxlcnQiLCJoYW5kbGVUb29sU2VsZWN0aW9uIiwidG9vbE5hbWUiLCJuZXdTZWxlY3RlZFRvb2xzIiwiaW5jbHVkZXMiLCJmaWx0ZXIiLCJ0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/hooks/useToolSettings.ts\n"));

/***/ })

});