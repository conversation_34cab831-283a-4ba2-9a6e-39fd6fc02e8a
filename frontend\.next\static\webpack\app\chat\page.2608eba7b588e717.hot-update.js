"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Check)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Check\", [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n]);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhQSxDQUFNLFVBQVEsb0VBQWlCLE9BQVM7SUFBQztRQUFDO1FBQVE7WUFBRSxHQUFHLGlCQUFtQjtZQUFBLEtBQUssQ0FBUztRQUFBLENBQUM7S0FBQztDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXHNyY1xcaWNvbnNcXGNoZWNrLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQ2hlY2tcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qQWdOaUE1SURFM2JDMDFMVFVpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hlY2tcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBDaGVjayA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZWNrJywgW1sncGF0aCcsIHsgZDogJ00yMCA2IDkgMTdsLTUtNScsIGtleTogJzFnbWYyYycgfV1dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hlY2s7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronUp\", [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFBLENBQU0sY0FBWSxvRUFBaUIsV0FBYTtJQUFDO1FBQUM7UUFBUTtZQUFFLEdBQUcsZ0JBQWtCO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcc3JjXFxpY29uc1xcY2hldnJvbi11cC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25VcFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TVRnZ01UVXROaTAyTFRZZ05pSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLXVwXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvblVwID0gY3JlYXRlTHVjaWRlSWNvbignQ2hldnJvblVwJywgW1sncGF0aCcsIHsgZDogJ20xOCAxNS02LTYtNiA2Jywga2V5OiAnMTUzdWR6JyB9XV0pO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uVXA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Copy\", [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n]);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* harmony import */ var _ToolContentDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToolContentDisplay */ \"(app-pages-browser)/./src/app/chat/components/ToolContentDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\n\nfunction MessageItem(param) {\n    let { message, showAIStatus, aiState } = param;\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    // 解析工具调用内容，创建步骤数据\n    const parseToolCallSteps = (content)=>{\n        const steps = [];\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') && content.includes('参数:') && content.includes('结果:')) {\n            const toolCallMatches = content.match(/调用工具:\\s*([^\\n]+)\\n参数:\\s*([^]*?)\\n\\n结果:\\s*([^]*?)(?=\\n\\n|$)/g);\n            if (toolCallMatches) {\n                toolCallMatches.forEach((match, index)=>{\n                    const toolNameMatch = match.match(/调用工具:\\s*([^\\n]+)/);\n                    const paramsMatch = match.match(/参数:\\s*([^]*?)\\n\\n结果:/);\n                    const resultMatch = match.match(/结果:\\s*([^]*?)(?=\\n\\n|$)/);\n                    if (toolNameMatch) {\n                        const toolName = toolNameMatch[1].trim();\n                        let params = {};\n                        let result = '';\n                        try {\n                            if (paramsMatch) {\n                                params = JSON.parse(paramsMatch[1].trim());\n                            }\n                        } catch (e) {\n                            var _paramsMatch_;\n                            params = {\n                                raw: (paramsMatch === null || paramsMatch === void 0 ? void 0 : (_paramsMatch_ = paramsMatch[1]) === null || _paramsMatch_ === void 0 ? void 0 : _paramsMatch_.trim()) || ''\n                            };\n                        }\n                        if (resultMatch) {\n                            result = resultMatch[1].trim();\n                        }\n                        // 添加执行步骤\n                        steps.push({\n                            id: \"execution-\".concat(index),\n                            type: 'execution',\n                            toolName,\n                            status: 'success',\n                            timestamp: new Date(),\n                            input: params\n                        });\n                        // 添加结果步骤\n                        steps.push({\n                            id: \"result-\".concat(index),\n                            type: 'result',\n                            toolName,\n                            status: result.includes('错误') || result.includes('失败') ? 'error' : 'success',\n                            timestamp: new Date(),\n                            output: result,\n                            error: result.includes('错误') || result.includes('失败') ? result : undefined\n                        });\n                    }\n                });\n            }\n        }\n        return steps;\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 116,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理工具调用结果的显示\n    const renderToolContent = (content)=>{\n        try {\n            const toolData = JSON.parse(content);\n            if (toolData.tool_name && toolData.result) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_3__.ToolResultSummary, {\n                    result: toolData.result,\n                    isSuccess: !content.includes('错误') && !content.includes('失败'),\n                    executionTime: toolData.execution_time\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this);\n            }\n        } catch (e) {\n            // 如果不是JSON格式，检查是否是简单的工具结果\n            if (content.includes('工具执行') || content.includes('调用工具')) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_3__.ToolContentDisplay, {\n                    content: content,\n                    type: \"output\",\n                    title: \"工具执行结果\",\n                    maxLines: 6\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息中的工具调用\n    const renderAssistantContent = (content)=>{\n        // 检查是否包含工具调用\n        if (content.includes('```json') && content.includes('tool_calls')) {\n            const parts = content.split('```');\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (part.startsWith('json') && part.includes('tool_calls')) {\n                        try {\n                            var _toolCall_tool_calls__function, _toolCall_tool_calls_, _toolCall_tool_calls, _toolCall_tool_calls__function1, _toolCall_tool_calls_1, _toolCall_tool_calls1;\n                            const jsonContent = part.replace('json\\n', '').trim();\n                            const toolCall = JSON.parse(jsonContent);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded p-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium mb-1\",\n                                        children: [\n                                            \"\\uD83D\\uDD27 调用工具: \",\n                                            (_toolCall_tool_calls = toolCall.tool_calls) === null || _toolCall_tool_calls === void 0 ? void 0 : (_toolCall_tool_calls_ = _toolCall_tool_calls[0]) === null || _toolCall_tool_calls_ === void 0 ? void 0 : (_toolCall_tool_calls__function = _toolCall_tool_calls_.function) === null || _toolCall_tool_calls__function === void 0 ? void 0 : _toolCall_tool_calls__function.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs opacity-75 overflow-x-auto\",\n                                        children: JSON.stringify((_toolCall_tool_calls1 = toolCall.tool_calls) === null || _toolCall_tool_calls1 === void 0 ? void 0 : (_toolCall_tool_calls_1 = _toolCall_tool_calls1[0]) === null || _toolCall_tool_calls_1 === void 0 ? void 0 : (_toolCall_tool_calls__function1 = _toolCall_tool_calls_1.function) === null || _toolCall_tool_calls__function1 === void 0 ? void 0 : _toolCall_tool_calls__function1.arguments, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, this);\n                        } catch (e) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: part\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 24\n                            }, this);\n                        }\n                    }\n                    return part.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: renderImageIfUrl(part)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 34\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 177,\n            columnNumber: 12\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 212,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 \".concat(isUser ? 'bg-blue-600 text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'),\n                        children: [\n                            showAIStatus && aiState && aiState.status === 'tool_calling' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner-small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-green-700 dark:text-green-300\",\n                                        children: aiState.toolName ? \"正在调用 \".concat(aiState.toolName, \"...\") : '正在调用工具...'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(message.content)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/ToolContentDisplay.tsx":
/*!********************************************************!*\
  !*** ./src/app/chat/components/ToolContentDisplay.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleToolContent: () => (/* binding */ SimpleToolContent),\n/* harmony export */   ToolContentDisplay: () => (/* binding */ ToolContentDisplay),\n/* harmony export */   ToolResultSummary: () => (/* binding */ ToolResultSummary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Copy!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ ToolContentDisplay,SimpleToolContent,ToolResultSummary auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction ToolContentDisplay(param) {\n    let { content, maxLines = 6, title, type = 'output', showCopyButton = true } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCopied, setIsCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [needsTruncation, setNeedsTruncation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formattedContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);\n    // 检查内容是否需要截断\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToolContentDisplay.useEffect\": ()=>{\n            if (contentRef.current) {\n                const lines = formattedContent.split('\\n');\n                setNeedsTruncation(lines.length > maxLines);\n            }\n        }\n    }[\"ToolContentDisplay.useEffect\"], [\n        formattedContent,\n        maxLines\n    ]);\n    const getDisplayContent = ()=>{\n        if (isExpanded || !needsTruncation) {\n            return formattedContent;\n        }\n        const lines = formattedContent.split('\\n');\n        return lines.slice(0, maxLines).join('\\n') + '\\n...';\n    };\n    const getTypeStyles = ()=>{\n        switch(type){\n            case 'input':\n                return {\n                    container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700',\n                    title: 'text-blue-700 dark:text-blue-300',\n                    content: 'bg-white dark:bg-gray-900'\n                };\n            case 'error':\n                return {\n                    container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700',\n                    title: 'text-red-700 dark:text-red-300',\n                    content: 'bg-red-50 dark:bg-red-900/10 text-red-800 dark:text-red-200'\n                };\n            default:\n                return {\n                    container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700',\n                    title: 'text-green-700 dark:text-green-300',\n                    content: 'bg-white dark:bg-gray-900'\n                };\n        }\n    };\n    const handleCopy = async ()=>{\n        try {\n            await navigator.clipboard.writeText(formattedContent);\n            setIsCopied(true);\n            setTimeout(()=>setIsCopied(false), 2000);\n        } catch (err) {\n            console.error('复制失败:', err);\n        }\n    };\n    const styles = getTypeStyles();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg \".concat(styles.container),\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-3 py-2 border-b border-current border-opacity-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium \".concat(styles.title),\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            showCopyButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCopy,\n                                className: \"p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors \".concat(styles.title),\n                                title: \"复制内容\",\n                                children: isCopied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this),\n                            needsTruncation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsExpanded(!isExpanded),\n                                className: \"p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors \".concat(styles.title),\n                                title: isExpanded ? \"收起\" : \"展开\",\n                                children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-3 h-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: contentRef,\n                        className: \"text-sm font-mono rounded border p-3 \".concat(styles.content, \" \").concat(!isExpanded && needsTruncation ? 'max-h-32 overflow-hidden' : 'max-h-96 overflow-y-auto'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"whitespace-pre-wrap break-words\",\n                            children: getDisplayContent()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    needsTruncation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsExpanded(!isExpanded),\n                            className: \"text-xs px-3 py-1 rounded-full border transition-colors hover:bg-black hover:bg-opacity-5 \".concat(styles.title),\n                            children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-3 h-3 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"收起内容\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-3 h-3 inline mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"显示完整内容 (\",\n                                    formattedContent.split('\\n').length - maxLines,\n                                    \" 行更多)\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolContentDisplay, \"X+JXE2n3bJL1AaXorfl3JnCy+NY=\");\n_c = ToolContentDisplay;\n// 简化版本的内容显示组件\nfunction SimpleToolContent(param) {\n    let { content, maxHeight = '8rem' } = param;\n    const formattedContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded p-3 overflow-y-auto\",\n        style: {\n            maxHeight\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            className: \"whitespace-pre-wrap break-words text-gray-800 dark:text-gray-200\",\n            children: formattedContent\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SimpleToolContent;\n// 工具结果摘要组件\nfunction ToolResultSummary(param) {\n    let { result, isSuccess = true, executionTime } = param;\n    _s1();\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getSummary = ()=>{\n        const content = typeof result === 'string' ? result : JSON.stringify(result);\n        if (content.length <= 100) return content;\n        return content.substring(0, 100) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg \".concat(isSuccess ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700' : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium \".concat(isSuccess ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'),\n                                children: isSuccess ? '执行成功' : '执行失败'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500\",\n                                children: [\n                                    executionTime,\n                                    \"ms\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700 dark:text-gray-300 mb-2\",\n                        children: getSummary()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    typeof result === 'string' && result.length > 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowDetails(!showDetails),\n                        className: \"text-xs text-blue-600 dark:text-blue-400 hover:underline\",\n                        children: showDetails ? '隐藏详情' : '查看详情'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-current border-opacity-20 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleToolContent, {\n                    content: result,\n                    maxHeight: \"12rem\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolContentDisplay.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s1(ToolResultSummary, \"n2rC7YX8Mzz154E9USQBvseY7a0=\");\n_c2 = ToolResultSummary;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ToolContentDisplay\");\n$RefreshReg$(_c1, \"SimpleToolContent\");\n$RefreshReg$(_c2, \"ToolResultSummary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolContentDisplay.tsx\n"));

/***/ })

});