/**
 * MCP服务器实现
 * 使用官方TypeScript SDK创建MCP服务器，提供工具给Ollama模型使用
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
/**
 * 创建并配置MCP服务器
 */
function createMcpServer() {
    const server = new McpServer({
        name: 'kun-agent-tools',
        version: '1.0.0'
    });
    // 注册获取当前时间工具
    server.tool('get_current_time', '获取当前的日期和时间', {
        format: z.enum(['iso', 'local', 'timestamp']).describe('时间格式：iso（ISO 8601格式）、local（本地格式）、timestamp（时间戳）')
    }, async ({ format }) => {
        const now = new Date();
        let timeString;
        switch (format) {
            case 'iso':
                timeString = now.toISOString();
                break;
            case 'local':
                timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                break;
            case 'timestamp':
                timeString = now.getTime().toString();
                break;
            default:
                timeString = now.toString();
        }
        return {
            content: [{
                    type: 'text',
                    text: `当前时间（${format}格式）：${timeString}`
                }]
        };
    });
    // 注册计算器工具
    server.tool('calculate', '执行基本的数学计算（加减乘除、幂运算、开方等）', {
        expression: z.string().describe('要计算的数学表达式，例如："2 + 3 * 4"、"Math.sqrt(16)"、"Math.pow(2, 3)"')
    }, async ({ expression }) => {
        try {
            // 安全的数学表达式计算
            // 只允许数字、基本运算符和Math对象的方法
            const safeExpression = expression
                .replace(/[^0-9+\-*/().\s]/g, '')
                .replace(/Math\.(sqrt|pow|abs|floor|ceil|round|sin|cos|tan|log|exp)\(/g, 'Math.$1(');
            // 使用Function构造器安全执行表达式
            const result = Function(`"use strict"; return (${safeExpression})`)();
            if (typeof result !== 'number' || !isFinite(result)) {
                throw new Error('计算结果无效');
            }
            return {
                content: [{
                        type: 'text',
                        text: `计算结果：${expression} = ${result}`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `计算错误：${error instanceof Error ? error.message : '未知错误'}`
                    }]
            };
        }
    });
    // 注册随机数生成工具
    server.tool('generate_random_number', '生成指定范围内的随机数', {
        min: z.number().describe('最小值（包含）'),
        max: z.number().describe('最大值（包含）'),
        count: z.number().min(1).max(100).default(1).describe('生成随机数的个数（1-100）')
    }, async ({ min, max, count = 1 }) => {
        if (min > max) {
            return {
                content: [{
                        type: 'text',
                        text: '错误：最小值不能大于最大值'
                    }]
            };
        }
        const numbers = [];
        for (let i = 0; i < count; i++) {
            const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
            numbers.push(randomNum);
        }
        return {
            content: [{
                    type: 'text',
                    text: count === 1
                        ? `生成的随机数：${numbers[0]}`
                        : `生成的${count}个随机数：${numbers.join(', ')}`
                }]
        };
    });
    // 注册文本处理工具
    server.tool('process_text', '对文本进行各种处理操作', {
        text: z.string().describe('要处理的文本'),
        operation: z.enum(['uppercase', 'lowercase', 'reverse', 'length', 'words']).describe('处理操作：uppercase（转大写）、lowercase（转小写）、reverse（反转）、length（计算长度）、words（统计单词数）')
    }, async ({ text, operation }) => {
        let result;
        switch (operation) {
            case 'uppercase':
                result = `转换为大写：${text.toUpperCase()}`;
                break;
            case 'lowercase':
                result = `转换为小写：${text.toLowerCase()}`;
                break;
            case 'reverse':
                result = `反转文本：${text.split('').reverse().join('')}`;
                break;
            case 'length':
                result = `文本长度：${text.length} 个字符`;
                break;
            case 'words':
                const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
                result = `单词数量：${wordCount} 个单词`;
                break;
            default:
                result = '未知操作';
        }
        return {
            content: [{
                    type: 'text',
                    text: result
                }]
        };
    });
    // 注册网络搜索工具
    server.tool('web_search', '使用DuckDuckGo搜索引擎进行网络搜索', {
        query: z.string().describe('搜索查询词'),
        num_results: z.number().min(1).max(10).default(5).describe('返回结果数量（1-10，默认5）')
    }, async ({ query, num_results = 5 }) => {
        try {
            const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;
            const response = await fetch(searchUrl);
            if (!response.ok) {
                throw new Error(`搜索请求失败: ${response.status}`);
            }
            const data = await response.json();
            if (!data.RelatedTopics || data.RelatedTopics.length === 0) {
                return {
                    content: [{
                            type: 'text',
                            text: `搜索"${query}"没有找到相关结果`
                        }]
                };
            }
            const results = data.RelatedTopics
                .slice(0, num_results)
                .map((topic, index) => {
                const text = topic.Text || '无描述';
                const url = topic.FirstURL || '无链接';
                return `${index + 1}. ${text}\n   链接: ${url}`;
            })
                .join('\n\n');
            return {
                content: [{
                        type: 'text',
                        text: `搜索"${query}"的结果：\n\n${results}`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `搜索失败：${error instanceof Error ? error.message : '未知错误'}`
                    }]
            };
        }
    });
    // 注册测试工具
    server.tool('test_tool', '用于测试MCP连接的简单工具', {
        message: z.string().describe('测试消息')
    }, async ({ message }) => {
        return {
            content: [{
                    type: 'text',
                    text: `测试成功！收到消息：${message}`
                }]
        };
    });
    return server;
}
/**
 * 启动MCP服务器（用于独立运行）
 */
async function startMcpServer() {
    const server = createMcpServer();
    const transport = new StdioServerTransport();
    console.error('MCP服务器启动中...');
    await server.connect(transport);
    console.error('MCP服务器已启动，等待连接...');
}
// 导出函数
export { createMcpServer, startMcpServer };
// 如果直接运行此文件，启动服务器
if (import.meta.url === `file://${process.argv[1]}`) {
    startMcpServer().catch(console.error);
}
