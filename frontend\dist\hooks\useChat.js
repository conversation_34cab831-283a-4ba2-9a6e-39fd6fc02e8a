'use client';
import { useState, useEffect } from 'react';
export function useChat() {
    const [models, setModels] = useState([]);
    const [conversations, setConversations] = useState([]);
    const [currentConversation, setCurrentConversation] = useState(null);
    const [messages, setMessages] = useState([]);
    const [selectedModel, setSelectedModel] = useState('');
    const [inputMessage, setInputMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isStreaming, setIsStreaming] = useState(false);
    const [error, setError] = useState(null);
    const [showModelSelector, setShowModelSelector] = useState(false);
    const [enableTools, setEnableTools] = useState(false);
    const [selectedTools, setSelectedTools] = useState([]);
    // 加载模型列表
    const loadModels = async () => {
        try {
            const response = await fetch('/api/models');
            const data = await response.json();
            if (data.success) {
                setModels(data.models);
                if (data.models.length > 0 && !selectedModel) {
                    setSelectedModel(data.models[0].name);
                }
            }
            else {
                setError(data.message || '加载模型失败');
            }
        }
        catch (err) {
            setError('无法连接到服务器');
        }
    };
    // 加载对话列表
    const loadConversations = async () => {
        try {
            const response = await fetch('/api/conversations');
            const data = await response.json();
            if (data.success) {
                setConversations(data.conversations);
            }
        }
        catch (err) {
            console.error('加载对话列表失败:', err);
        }
    };
    // 加载特定对话
    const loadConversation = async (conversationId) => {
        try {
            const response = await fetch(`/api/conversations/${conversationId}`);
            const data = await response.json();
            if (data.success) {
                setCurrentConversation(data.conversation);
                setMessages(data.conversation.messages || []);
                setSelectedModel(data.conversation.model);
            }
        }
        catch (err) {
            console.error('加载对话失败:', err);
        }
    };
    // 创建新对话
    const createNewConversation = async () => {
        if (!selectedModel) {
            setError('请先选择一个模型');
            return;
        }
        try {
            const response = await fetch('/api/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: '新对话',
                    model: selectedModel,
                }),
            });
            const data = await response.json();
            if (data.success) {
                setCurrentConversation(data.conversation);
                setMessages([]);
                await loadConversations();
            }
        }
        catch (err) {
            setError('创建对话失败');
        }
    };
    // 删除对话
    const deleteConversation = async (conversationId) => {
        if (!confirm('确定要删除这个对话吗？')) {
            return;
        }
        try {
            const response = await fetch(`/api/conversations/${conversationId}`, {
                method: 'DELETE',
            });
            if (response.ok) {
                if (currentConversation?.id === conversationId) {
                    setCurrentConversation(null);
                    setMessages([]);
                }
                await loadConversations();
            }
        }
        catch (err) {
            setError('删除对话失败');
        }
    };
    // 发送消息
    const sendMessage = async () => {
        if (!inputMessage.trim() || !selectedModel || isStreaming) {
            return;
        }
        if (!currentConversation) {
            await createNewConversation();
            // 等待对话创建完成
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        const userMessage = {
            role: 'user',
            content: inputMessage.trim(),
        };
        // 添加用户消息到界面
        const newUserMessage = {
            id: Date.now(),
            conversation_id: currentConversation?.id || 0,
            role: 'user',
            content: userMessage.content,
            created_at: new Date().toISOString(),
        };
        setMessages(prev => [...prev, newUserMessage]);
        setInputMessage('');
        setIsStreaming(true);
        setError(null);
        try {
            // 准备发送的消息历史
            const chatMessages = [
                ...messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                })),
                userMessage,
            ];
            const requestBody = {
                model: selectedModel,
                messages: chatMessages,
                conversationId: currentConversation?.id,
                stream: true,
                enableTools: enableTools,
                selectedTools: selectedTools,
            };
            console.log('前端发送请求:', requestBody);
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
            });
            if (!response.ok) {
                throw new Error('聊天请求失败');
            }
            const reader = response.body?.getReader();
            const decoder = new TextDecoder();
            if (!reader) {
                throw new Error('无法读取响应');
            }
            // 创建助手消息
            const assistantMessage = {
                id: Date.now() + 1,
                conversation_id: currentConversation?.id || 0,
                role: 'assistant',
                content: '',
                created_at: new Date().toISOString(),
            };
            setMessages(prev => [...prev, assistantMessage]);
            let assistantContent = '';
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') {
                            setIsStreaming(false);
                            await loadConversations(); // 刷新对话列表
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.error) {
                                throw new Error(parsed.message || '聊天出错');
                            }
                            if (parsed.message?.content) {
                                assistantContent += parsed.message.content;
                                setMessages(prev => prev.map(msg => msg.id === assistantMessage.id
                                    ? { ...msg, content: assistantContent }
                                    : msg));
                            }
                        }
                        catch (parseError) {
                            // 忽略解析错误，继续处理下一行
                        }
                    }
                }
            }
        }
        catch (err) {
            setError(err instanceof Error ? err.message : '发送消息失败');
            // 移除失败的用户消息
            setMessages(prev => prev.filter(msg => msg.id !== newUserMessage.id));
        }
        finally {
            setIsStreaming(false);
        }
    };
    // 初始化
    useEffect(() => {
        loadModels();
        loadConversations();
    }, []);
    const handleToolsToggle = (enabled) => {
        setEnableTools(enabled);
    };
    return {
        // 状态
        models,
        conversations,
        currentConversation,
        messages,
        selectedModel,
        inputMessage,
        isLoading,
        isStreaming,
        error,
        showModelSelector,
        enableTools,
        selectedTools,
        // 设置函数
        setSelectedModel,
        setInputMessage,
        setError,
        setShowModelSelector,
        setEnableTools,
        setSelectedTools,
        // 操作函数
        loadConversation,
        createNewConversation,
        deleteConversation,
        sendMessage,
        onToolsToggle: handleToolsToggle,
    };
}
