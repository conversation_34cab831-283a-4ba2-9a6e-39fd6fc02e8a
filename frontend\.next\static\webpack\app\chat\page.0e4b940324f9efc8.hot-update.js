"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\nfunction MessageItem(param) {\n    let { message, showAIStatus, aiState } = param;\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 57,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理工具调用结果的显示\n    const renderToolContent = (content)=>{\n        try {\n            const toolData = JSON.parse(content);\n            if (toolData.tool_name && toolData.result) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm\",\n                            children: [\n                                \"\\uD83D\\uDD27 \",\n                                toolData.tool_name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90\",\n                            children: typeof toolData.result === 'string' ? renderImageIfUrl(toolData.result) : renderImageIfUrl(JSON.stringify(toolData.result, null, 2))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this);\n            }\n        } catch (e) {\n        // 如果不是JSON格式，直接显示原内容并检测图片\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 82,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息中的工具调用\n    const renderAssistantContent = (content)=>{\n        // 检查是否包含工具调用\n        if (content.includes('```json') && content.includes('tool_calls')) {\n            const parts = content.split('```');\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (part.startsWith('json') && part.includes('tool_calls')) {\n                        try {\n                            var _toolCall_tool_calls__function, _toolCall_tool_calls_, _toolCall_tool_calls, _toolCall_tool_calls__function1, _toolCall_tool_calls_1, _toolCall_tool_calls1;\n                            const jsonContent = part.replace('json\\n', '').trim();\n                            const toolCall = JSON.parse(jsonContent);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded p-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium mb-1\",\n                                        children: [\n                                            \"\\uD83D\\uDD27 调用工具: \",\n                                            (_toolCall_tool_calls = toolCall.tool_calls) === null || _toolCall_tool_calls === void 0 ? void 0 : (_toolCall_tool_calls_ = _toolCall_tool_calls[0]) === null || _toolCall_tool_calls_ === void 0 ? void 0 : (_toolCall_tool_calls__function = _toolCall_tool_calls_.function) === null || _toolCall_tool_calls__function === void 0 ? void 0 : _toolCall_tool_calls__function.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs opacity-75 overflow-x-auto\",\n                                        children: JSON.stringify((_toolCall_tool_calls1 = toolCall.tool_calls) === null || _toolCall_tool_calls1 === void 0 ? void 0 : (_toolCall_tool_calls_1 = _toolCall_tool_calls1[0]) === null || _toolCall_tool_calls_1 === void 0 ? void 0 : (_toolCall_tool_calls__function1 = _toolCall_tool_calls_1.function) === null || _toolCall_tool_calls__function1 === void 0 ? void 0 : _toolCall_tool_calls__function1.arguments, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 19\n                            }, this);\n                        } catch (e) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: part\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 24\n                            }, this);\n                        }\n                    }\n                    return part.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: renderImageIfUrl(part)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 34\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 114,\n            columnNumber: 12\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 \".concat(isUser ? 'bg-blue-600 text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'),\n                        children: [\n                            showAIStatus && aiState && aiState.status === 'tool_calling' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner-small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-green-700 dark:text-green-300\",\n                                        children: aiState.toolName ? \"正在调用 \".concat(aiState.toolName, \"...\") : '正在调用工具...'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(message.content)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ })

});