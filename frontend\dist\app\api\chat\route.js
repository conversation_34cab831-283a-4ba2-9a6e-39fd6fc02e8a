import { NextResponse } from 'next/server';
import { ollamaClient } from '../../../lib/ollama';
import { dbOperations } from '../../../lib/database';
import { ToolExecutor, testTool, getToolsByNames } from '../../../lib/tools';
import { mcpServerClient } from '../../../lib/mcp/mcp-client-server';
export async function POST(request) {
    try {
        // 确保MCP服务器客户端已连接
        if (!mcpServerClient.isClientConnected()) {
            await mcpServerClient.connect();
        }
        const body = await request.json();
        const { model, messages, conversationId, stream = true, options = {}, enableTools = true, testMode = false, selectedTools = [] } = body;
        // 测试模式：仅验证模型是否支持工具，使用简单的测试工具
        if (testMode) {
            try {
                const testResponse = await ollamaClient.chat({
                    model,
                    messages: [{ role: 'user', content: 'test' }],
                    tools: enableTools ? [testTool] : undefined,
                    stream: false,
                });
                return NextResponse.json({ success: true, supportsTools: true });
            }
            catch (error) {
                const errorMessage = error.message || '';
                if (errorMessage.includes('does not support tools')) {
                    return NextResponse.json({ success: true, supportsTools: false });
                }
                return NextResponse.json({ success: false, error: errorMessage });
            }
        }
        // 验证必需参数
        if (!model || !messages || !Array.isArray(messages) || messages.length === 0) {
            return NextResponse.json({ error: '缺少必需参数: model 和 messages' }, { status: 400 });
        }
        // 检查Ollama服务是否可用
        const isAvailable = await ollamaClient.isAvailable();
        if (!isAvailable) {
            return NextResponse.json({
                error: 'Ollama服务不可用',
                message: '请确保Ollama正在运行并监听在localhost:11434端口'
            }, { status: 503 });
        }
        // 如果是流式响应
        if (stream) {
            const encoder = new TextEncoder();
            const readableStream = new ReadableStream({
                async start(controller) {
                    try {
                        let assistantMessage = '';
                        // 准备聊天请求，如果启用工具则添加用户选择的工具定义
                        let userSelectedTools = [];
                        if (enableTools && selectedTools.length > 0) {
                            userSelectedTools = await getToolsByNames(selectedTools);
                            console.log('用户选择的工具:', selectedTools);
                            console.log('获取到的工具定义:', userSelectedTools);
                        }
                        let chatRequest = {
                            model,
                            messages,
                            stream: true,
                            options,
                            ...(enableTools && userSelectedTools.length > 0 && { tools: userSelectedTools })
                        };
                        console.log('发送聊天请求:', JSON.stringify(chatRequest, null, 2));
                        console.log('enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);
                        let retryWithoutTools = false;
                        try {
                            // 使用流式API
                            for await (const chunk of ollamaClient.chatStream(chatRequest)) {
                                // 处理工具调用
                                if (chunk.message?.tool_calls && chunk.message.tool_calls.length > 0) {
                                    // 执行工具调用
                                    for (const toolCall of chunk.message.tool_calls) {
                                        try {
                                            // 安全解析工具调用参数
                                            let args = {};
                                            if (toolCall.function.arguments) {
                                                if (typeof toolCall.function.arguments === 'string') {
                                                    try {
                                                        args = JSON.parse(toolCall.function.arguments);
                                                    }
                                                    catch (parseError) {
                                                        console.error('工具参数JSON解析失败:', parseError, '原始参数:', toolCall.function.arguments);
                                                        throw new Error(`工具参数格式错误: ${toolCall.function.arguments}`);
                                                    }
                                                }
                                                else if (typeof toolCall.function.arguments === 'object') {
                                                    args = toolCall.function.arguments;
                                                }
                                            }
                                            const result = await ToolExecutor.executeToolCall(toolCall.function.name, args);
                                            // 发送工具调用结果
                                            const toolResultData = `data: ${JSON.stringify({
                                                message: {
                                                    role: 'tool',
                                                    content: `工具 ${toolCall.function.name} 执行结果: ${result}`,
                                                    tool_call_id: toolCall.id
                                                }
                                            })}\n\n`;
                                            controller.enqueue(encoder.encode(toolResultData));
                                            // 将工具结果添加到消息历史中，继续对话
                                            const updatedMessages = [
                                                ...messages,
                                                {
                                                    role: 'assistant',
                                                    content: '',
                                                    tool_calls: [toolCall]
                                                },
                                                {
                                                    role: 'tool',
                                                    content: result
                                                }
                                            ];
                                            // 继续对话以获取基于工具结果的回复
                                            const followUpRequest = {
                                                model,
                                                messages: updatedMessages,
                                                stream: true,
                                                options
                                            };
                                            for await (const followUpChunk of ollamaClient.chatStream(followUpRequest)) {
                                                if (followUpChunk.message?.content) {
                                                    assistantMessage += followUpChunk.message.content;
                                                }
                                                const followUpData = `data: ${JSON.stringify(followUpChunk)}\n\n`;
                                                controller.enqueue(encoder.encode(followUpData));
                                                if (followUpChunk.done) {
                                                    break;
                                                }
                                            }
                                        }
                                        catch (toolError) {
                                            console.error('工具执行失败:', toolError);
                                            const errorResult = `工具执行失败: ${toolError instanceof Error ? toolError.message : '未知错误'}`;
                                            const toolErrorData = `data: ${JSON.stringify({
                                                message: {
                                                    role: 'tool',
                                                    content: errorResult,
                                                    tool_call_id: toolCall.id
                                                }
                                            })}\n\n`;
                                            controller.enqueue(encoder.encode(toolErrorData));
                                        }
                                    }
                                }
                                else {
                                    // 累积助手的回复内容
                                    if (chunk.message?.content) {
                                        assistantMessage += chunk.message.content;
                                    }
                                    // 发送数据块到客户端
                                    const data = `data: ${JSON.stringify(chunk)}\n\n`;
                                    controller.enqueue(encoder.encode(data));
                                }
                                // 如果完成，保存消息到数据库
                                if (chunk.done && conversationId && assistantMessage.trim()) {
                                    try {
                                        // 保存用户消息（如果还没保存）
                                        const lastUserMessage = messages[messages.length - 1];
                                        if (lastUserMessage?.role === 'user') {
                                            dbOperations.createMessage({
                                                conversation_id: conversationId,
                                                role: 'user',
                                                content: lastUserMessage.content
                                            });
                                        }
                                        // 保存助手回复
                                        dbOperations.createMessage({
                                            conversation_id: conversationId,
                                            role: 'assistant',
                                            content: assistantMessage
                                        });
                                    }
                                    catch (dbError) {
                                        console.error('保存消息到数据库失败:', dbError);
                                    }
                                }
                            }
                            // 发送结束标志
                            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                            controller.close();
                        }
                        catch (streamError) {
                            console.error('流式请求错误:', streamError);
                            // 检查是否是模型不支持工具的错误
                            const errorMessage = streamError instanceof Error ? streamError.message : String(streamError);
                            const isToolsNotSupported = errorMessage.includes('does not support tools');
                            // 如果启用了工具且出现工具不支持错误，尝试不使用工具重新请求
                            if (enableTools && !retryWithoutTools && isToolsNotSupported) {
                                console.log('模型不支持工具调用，尝试不使用工具重新请求');
                                retryWithoutTools = true;
                                // 重新构建不包含工具的请求
                                chatRequest = {
                                    model,
                                    messages,
                                    stream: true,
                                    options
                                };
                                // 重新尝试流式API
                                for await (const chunk of ollamaClient.chatStream(chatRequest)) {
                                    // 累积助手的回复内容
                                    if (chunk.message?.content) {
                                        assistantMessage += chunk.message.content;
                                    }
                                    // 发送数据块到客户端
                                    const data = `data: ${JSON.stringify(chunk)}\n\n`;
                                    controller.enqueue(encoder.encode(data));
                                    // 如果完成，保存消息到数据库
                                    if (chunk.done && conversationId && assistantMessage.trim()) {
                                        try {
                                            // 保存用户消息（如果还没保存）
                                            const lastUserMessage = messages[messages.length - 1];
                                            if (lastUserMessage?.role === 'user') {
                                                dbOperations.createMessage({
                                                    conversation_id: conversationId,
                                                    role: 'user',
                                                    content: lastUserMessage.content
                                                });
                                            }
                                            // 保存助手回复
                                            dbOperations.createMessage({
                                                conversation_id: conversationId,
                                                role: 'assistant',
                                                content: assistantMessage
                                            });
                                        }
                                        catch (dbError) {
                                            console.error('保存消息到数据库失败:', dbError);
                                        }
                                    }
                                }
                                // 发送结束标志
                                controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                                controller.close();
                            }
                            else {
                                // 如果已经重试过或者没有启用工具，或者不是工具不支持的错误，直接抛出错误
                                throw streamError;
                            }
                        }
                    }
                    catch (error) {
                        console.error('流式聊天失败:', error);
                        const errorData = `data: ${JSON.stringify({
                            error: true,
                            message: error instanceof Error ? error.message : '聊天请求失败'
                        })}\n\n`;
                        controller.enqueue(encoder.encode(errorData));
                        controller.close();
                    }
                }
            });
            return new Response(readableStream, {
                headers: {
                    'Content-Type': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                },
            });
        }
        else {
            // 非流式响应
            const userSelectedTools = enableTools && selectedTools.length > 0
                ? await getToolsByNames(selectedTools)
                : [];
            const chatRequest = {
                model,
                messages,
                stream: false,
                options,
                ...(enableTools && userSelectedTools.length > 0 && { tools: userSelectedTools })
            };
            console.log('非流式聊天请求:', JSON.stringify(chatRequest, null, 2));
            console.log('非流式 enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);
            let response = await ollamaClient.chat(chatRequest);
            let finalMessages = [...messages];
            // 处理工具调用
            if (response.message?.tool_calls && response.message.tool_calls.length > 0) {
                for (const toolCall of response.message.tool_calls) {
                    try {
                        const args = JSON.parse(toolCall.function.arguments);
                        const result = await ToolExecutor.executeToolCall(toolCall.function.name, args);
                        // 添加工具调用和结果到消息历史
                        finalMessages.push({
                            role: 'assistant',
                            content: '',
                            tool_calls: [toolCall]
                        });
                        finalMessages.push({
                            role: 'tool',
                            content: result
                        });
                        // 继续对话以获取基于工具结果的回复
                        const followUpResponse = await ollamaClient.chat({
                            model,
                            messages: finalMessages,
                            stream: false,
                            options
                        });
                        response = followUpResponse;
                    }
                    catch (toolError) {
                        console.error('工具执行失败:', toolError);
                        // 在工具执行失败时，返回错误信息
                        response.message.content = `工具执行失败: ${toolError instanceof Error ? toolError.message : '未知错误'}`;
                    }
                }
            }
            // 保存消息到数据库
            if (conversationId) {
                try {
                    // 保存用户消息
                    const lastUserMessage = messages[messages.length - 1];
                    if (lastUserMessage?.role === 'user') {
                        dbOperations.createMessage({
                            conversation_id: conversationId,
                            role: 'user',
                            content: lastUserMessage.content
                        });
                    }
                    // 保存助手回复
                    if (response.message?.content) {
                        dbOperations.createMessage({
                            conversation_id: conversationId,
                            role: 'assistant',
                            content: response.message.content
                        });
                    }
                }
                catch (dbError) {
                    console.error('保存消息到数据库失败:', dbError);
                }
            }
            return NextResponse.json({
                success: true,
                response
            });
        }
    }
    catch (error) {
        console.error('ollama.ts chatStream 详细错误:', error);
        return NextResponse.json({
            error: '聊天请求失败',
            message: error instanceof Error ? `流式聊天请求失败: ${error.message}` : '流式聊天请求失败，请检查网络连接和Ollama服务状态'
        }, { status: 500 });
    }
}
