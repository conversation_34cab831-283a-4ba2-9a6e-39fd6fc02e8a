'use client';

import React from 'react';
import { Cog, Zap, Activity } from 'lucide-react';

interface ToolLoadingAnimationProps {
  toolName?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showProgress?: boolean;
  progress?: number;
}

export function ToolLoadingAnimation({ 
  toolName, 
  variant = 'default',
  showProgress = false,
  progress = 0
}: ToolLoadingAnimationProps) {
  
  if (variant === 'compact') {
    return (
      <div className="flex items-center gap-2">
        <div className="w-4 h-4 relative">
          <div className="absolute inset-0 border-2 border-blue-200 rounded-full"></div>
          <div className="absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
        </div>
        <span className="text-sm text-blue-600">
          {toolName ? `执行 ${toolName}...` : '执行中...'}
        </span>
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
        <div className="flex items-center gap-4">
          {/* 主要动画 */}
          <div className="relative">
            <div className="w-12 h-12 relative">
              {/* 外圈旋转 */}
              <div className="absolute inset-0 border-4 border-blue-200 rounded-full"></div>
              <div className="absolute inset-0 border-4 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
              
              {/* 内部图标 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <Cog className="w-6 h-6 text-blue-600 animate-pulse" />
              </div>
            </div>
            
            {/* 脉冲效果 */}
            <div className="absolute inset-0 w-12 h-12 bg-blue-400 rounded-full opacity-20 animate-ping"></div>
          </div>

          {/* 文本信息 */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                MCP工具调用中
              </span>
            </div>
            
            {toolName && (
              <div className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                正在执行: <span className="font-mono bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded">{toolName}</span>
              </div>
            )}
            
            {/* 进度条 */}
            {showProgress && (
              <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                ></div>
              </div>
            )}
            
            {/* 活动指示器 */}
            <div className="flex items-center gap-1 mt-2">
              <Activity className="w-3 h-3 text-blue-500" />
              <div className="flex gap-1">
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce"></div>
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-1 h-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span className="text-xs text-blue-600 dark:text-blue-400 ml-2">
                处理中...
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 默认样式
  return (
    <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
      {/* 旋转齿轮动画 */}
      <div className="relative">
        <div className="w-6 h-6 relative">
          <div className="absolute inset-0 border-2 border-blue-200 rounded-full"></div>
          <div className="absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
        </div>
      </div>

      {/* 文本信息 */}
      <div>
        <div className="font-medium text-sm text-blue-800 dark:text-blue-200">
          {toolName ? `正在执行: ${toolName}` : '正在调用工具'}
        </div>
        <div className="text-xs text-blue-600 dark:text-blue-300">
          请稍候...
        </div>
      </div>

      {/* 波浪动画 */}
      <div className="flex items-center gap-1 ml-auto">
        <div className="w-1 h-4 bg-blue-400 rounded-full animate-pulse"></div>
        <div className="w-1 h-6 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-1 h-4 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
      </div>
    </div>
  );
}

// 简化的工具执行状态指示器
export function ToolExecutionIndicator({ 
  toolName, 
  status = 'running' 
}: { 
  toolName: string; 
  status?: 'running' | 'success' | 'error' 
}) {
  const getStatusConfig = () => {
    switch (status) {
      case 'running':
        return {
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-700',
          icon: (
            <div className="w-4 h-4 relative">
              <div className="absolute inset-0 border-2 border-blue-200 rounded-full"></div>
              <div className="absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
            </div>
          )
        };
      case 'success':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-700',
          icon: <div className="w-4 h-4 bg-green-600 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        };
      case 'error':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50 dark:bg-red-900/20',
          borderColor: 'border-red-200 dark:border-red-700',
          icon: <div className="w-4 h-4 bg-red-600 rounded-full flex items-center justify-center">
            <div className="w-2 h-1 bg-white rounded-full"></div>
          </div>
        };
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-700',
          icon: <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border ${config.bgColor} ${config.borderColor}`}>
      {config.icon}
      <span className={`text-sm font-medium ${config.color}`}>
        {toolName}
      </span>
    </div>
  );
}
