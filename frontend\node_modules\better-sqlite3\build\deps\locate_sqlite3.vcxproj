<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{62FA9DED-AB83-9B3C-79DA-728C14F8CF8F}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>locate_sqlite3</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\..\deps\bin\;$(MSBuildProjectDirectory)\..\..\deps\bin\</ExecutablePath>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetName>$(ProjectName)</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=locate_sqlite3;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;SQLITE_DEBUG;SQLITE_MEMDEBUG;SQLITE_ENABLE_API_ARMOR;SQLITE_WIN32_MALLOC_VALIDATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.15.0\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=locate_sqlite3;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;SQLITE_DEBUG;SQLITE_MEMDEBUG;SQLITE_ENABLE_API_ARMOR;SQLITE_WIN32_MALLOC_VALIDATE;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=locate_sqlite3;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.15.0\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.15.0\deps\v8\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=locate_sqlite3;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;HOST_BINARY=&quot;node.exe&quot;;NDEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\deps\sqlite3.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\deps\sqlite3\sqlite3.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="..\..\deps\sqlite3\sqlite3ext.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\..\deps\sqlite3\sqlite3.c">
      <FileType>Document</FileType>
      <Command>call call &quot;node&quot; &quot;../../deps/copy.js&quot; &quot;$(OutDir)obj/global_intermediate/sqlite3&quot; &quot;&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>copy_builtin_sqlite3</Message>
      <Outputs>$(OutDir)\obj\global_intermediate\sqlite3\sqlite3.c;$(OutDir)\obj\global_intermediate\sqlite3\sqlite3.h;$(OutDir)\obj\global_intermediate\sqlite3\sqlite3ext.h</Outputs>
      <AdditionalInputs>..\..\deps\sqlite3\sqlite3.h;..\..\deps\sqlite3\sqlite3ext.h</AdditionalInputs>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
