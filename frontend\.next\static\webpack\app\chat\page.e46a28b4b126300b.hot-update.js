"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/ToolToggle.tsx":
/*!************************************************!*\
  !*** ./src/app/chat/components/ToolToggle.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolToggle: () => (/* binding */ ToolToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Settings,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ ToolToggle auto */ \n\n\nfunction ToolToggle(param) {\n    let { enableTools, isCheckingModel, modelSupportsTools, showToolSettings, selectedToolsCount, onToolsToggle, onShowToolSettings, onClearChat } = param;\n    const handleClick = ()=>{\n        // 如果模型不支持工具调用，则不执行任何操作\n        if (modelSupportsTools === false) {\n            return;\n        }\n        if (enableTools) {\n            // 如果工具已启用，点击切换设置面板\n            onShowToolSettings(!showToolSettings);\n        } else {\n            // 如果工具未启用，点击启用工具\n            onToolsToggle();\n        }\n    };\n    // 判断按钮是否应该被禁用\n    const isDisabled = isCheckingModel || modelSupportsTools === false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClick,\n                        disabled: isDisabled,\n                        className: \"relative w-10 h-10 rounded-full border-2 transition-all duration-200 flex items-center justify-center \".concat(isDisabled ? 'border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60' : enableTools && modelSupportsTools !== false ? 'border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-900/20' : 'border-gray-400 dark:border-gray-500 text-gray-500 dark:text-gray-400 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800'),\n                        children: [\n                            isCheckingModel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 rounded-full border-2 border-transparent border-t-current animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-5 h-5 relative z-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            enableTools && selectedToolsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-2 -left-2 min-w-[20px] h-[20px] bg-blue-500 text-white text-xs rounded-full border-2 border-white dark:border-gray-900 z-20 flex items-center justify-center px-1 font-medium shadow-sm\",\n                                children: selectedToolsCount > 99 ? '99+' : selectedToolsCount\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            modelSupportsTools !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 z-20 shadow-sm \".concat(modelSupportsTools ? 'bg-green-500' : 'bg-red-500')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50\",\n                        children: modelSupportsTools === false ? '当前模型不支持工具调用' : enableTools ? showToolSettings ? '关闭工具设置' : '打开工具设置' : '启用工具调用'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            onClearChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClearChat,\n                        className: \"relative w-10 h-10 rounded-full transition-all duration-200 flex items-center justify-center  dark:border-gray-500 text-gray-500 dark:text-gray-400 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 hover:border-red-400 hover:text-red-500 dark:hover:border-red-400 dark:hover:text-red-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Settings_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50\",\n                        children: \"清空当前对话\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolToggle.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c = ToolToggle;\nvar _c;\n$RefreshReg$(_c, \"ToolToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolToggle.tsx\n"));

/***/ })

});