{"name": "@unrs/resolver-binding-win32-x64-msvc", "version": "1.7.13", "cpu": ["x64"], "main": "resolver.win32-x64-msvc.node", "files": ["resolver.win32-x64-msvc.node"], "description": "UnRS Resolver Node API with PNP support", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "homepage": "https://github.com/unrs/unrs-resolver#readme", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "repository": "git+https://github.com/unrs/unrs-resolver.git", "os": ["win32"]}