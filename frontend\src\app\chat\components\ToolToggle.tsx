'use client';

import React from 'react';
import { Settings, Trash2 } from 'lucide-react';

interface ToolToggleProps {
  enableTools: boolean;
  isCheckingModel: boolean;
  modelSupportsTools: boolean | null;
  showToolSettings: boolean;
  selectedToolsCount: number;
  onToolsToggle: () => void;
  onShowToolSettings: (show: boolean) => void;
  onClearChat?: () => void;
}

export function ToolToggle({
  enableTools,
  isCheckingModel,
  modelSupportsTools,
  showToolSettings,
  selectedToolsCount,
  onToolsToggle,
  onShowToolSettings,
  onClearChat,
}: ToolToggleProps) {
  const handleClick = () => {
    // 如果模型不支持工具调用，则不执行任何操作
    if (modelSupportsTools === false) {
      return;
    }
    
    if (enableTools) {
      // 如果工具已启用，点击切换设置面板
      onShowToolSettings(!showToolSettings);
    } else {
      // 如果工具未启用，点击启用工具
      onToolsToggle();
    }
  };

  // 判断按钮是否应该被禁用
  const isDisabled = isCheckingModel || modelSupportsTools === false;

  return (
    <div className="flex items-center gap-2">
      {/* MCP工具调用按钮 */}
      <div className="relative group">
        <button
          onClick={handleClick}
          disabled={isDisabled}
          className={`relative w-10 h-10 rounded-full border-2 transition-all duration-200 flex items-center justify-center ${
            isDisabled
              ? 'border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-60'
              : enableTools && modelSupportsTools !== false
                ? 'border-blue-500 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-900/20'
                : 'border-gray-400 dark:border-gray-500 text-gray-500 dark:text-gray-400 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800'
          }`}
        >
          {/* Loading 描边动画 */}
          {isCheckingModel && (
            <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-current animate-spin" />
          )}
          
          <Settings className="w-5 h-5 relative z-10" />
          
          {/* 工具数量指示器 - 左上角 */}
          {enableTools && selectedToolsCount > 0 && (
            <div className="absolute -top-2 -left-2 min-w-[20px] h-[20px] bg-blue-500 text-white text-xs rounded-full border-2 border-white dark:border-gray-900 z-20 flex items-center justify-center px-1 font-medium shadow-sm">
              {selectedToolsCount > 99 ? '99+' : selectedToolsCount}
            </div>
          )}

          {/* 支持状态指示点 - 右上角 */}
          {modelSupportsTools !== null && (
            <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 z-20 shadow-sm ${
              modelSupportsTools
                ? 'bg-green-500'
                : 'bg-red-500'
            }`} />
          )}
        </button>
        
        {/* 工具提示 */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 shadow-lg">
          {isCheckingModel
            ? '正在检查模型兼容性...'
            : modelSupportsTools === false
              ? '当前模型不支持工具调用，请选择其他模型'
              : enableTools
                ? (showToolSettings ? '关闭工具设置' : `打开工具设置 (已选择 ${selectedToolsCount} 个工具)`)
                : '启用工具调用功能'
          }
        </div>
      </div>

      {/* 清空对话按钮 */}
      {onClearChat && (
        <div className="relative group">
          <button
            onClick={onClearChat}
            className="relative w-10 h-10 rounded-full transition-all duration-200 flex items-center justify-center  dark:border-gray-500 text-gray-500 dark:text-gray-400 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 hover:border-red-400 hover:text-red-500 dark:hover:border-red-400 dark:hover:text-red-400"
          >
            <Trash2 className="w-5 h-5" />
          </button>
          
          {/* 工具提示 */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
            清空当前对话
          </div>
        </div>
      )}
    </div>
  );
}