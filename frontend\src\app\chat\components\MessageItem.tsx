'use client';

import React from 'react';
import { <PERSON><PERSON>, User, Wrench, Info } from 'lucide-react';
import { Message } from '@/lib/database';
import { AIState } from '../types';
import { AIStatusIndicator } from './AIStatusIndicator';
import { ToolCallVisualization } from './ToolCallVisualization';
import { ToolLoadingAnimation } from './ToolLoadingAnimation';
import { ToolContentDisplay, ToolResultSummary } from './ToolContentDisplay';

interface MessageItemProps {
  message: Message;
  showAIStatus?: boolean;
  aiState?: AIState;
}

export function MessageItem({ message, showAIStatus, aiState }: MessageItemProps) {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isTool = message.role === 'tool';

  // 解析工具调用内容，创建步骤数据
  const parseToolCallSteps = (content: string) => {
    const steps: any[] = [];

    // 检查是否包含工具调用信息
    if (content.includes('调用工具:') && content.includes('参数:') && content.includes('结果:')) {
      const toolCallMatches = content.match(/调用工具:\s*([^\n]+)\n参数:\s*([^]*?)\n\n结果:\s*([^]*?)(?=\n\n|$)/g);

      if (toolCallMatches) {
        toolCallMatches.forEach((match, index) => {
          const toolNameMatch = match.match(/调用工具:\s*([^\n]+)/);
          const paramsMatch = match.match(/参数:\s*([^]*?)\n\n结果:/);
          const resultMatch = match.match(/结果:\s*([^]*?)(?=\n\n|$)/);

          if (toolNameMatch) {
            const toolName = toolNameMatch[1].trim();
            let params = {};
            let result = '';

            try {
              if (paramsMatch) {
                params = JSON.parse(paramsMatch[1].trim());
              }
            } catch (e) {
              params = { raw: paramsMatch?.[1]?.trim() || '' };
            }

            if (resultMatch) {
              result = resultMatch[1].trim();
            }

            // 添加执行步骤
            steps.push({
              id: `execution-${index}`,
              type: 'execution',
              toolName,
              status: 'success',
              timestamp: new Date(),
              input: params
            });

            // 添加结果步骤
            steps.push({
              id: `result-${index}`,
              type: 'result',
              toolName,
              status: result.includes('错误') || result.includes('失败') ? 'error' : 'success',
              timestamp: new Date(),
              output: result,
              error: result.includes('错误') || result.includes('失败') ? result : undefined
            });
          }
        });
      }
    }

    return steps;
  };

  // 检测并渲染图片URL
  const renderImageIfUrl = (text: string) => {
    const imageUrlRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp|svg))/gi;
    const matches = text.match(imageUrlRegex);
    
    if (matches) {
      const parts = text.split(imageUrlRegex);
      return (
        <div className="space-y-2">
          {parts.map((part, index) => {
            if (imageUrlRegex.test(part)) {
              return (
                <div key={index} className="my-2">
                  <img 
                    src={part} 
                    alt="工具返回的图片" 
                    className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden text-sm text-gray-500 italic">
                    图片加载失败: {part}
                  </div>
                </div>
              );
            }
            return part ? <span key={index}>{part}</span> : null;
          })}
        </div>
      );
    }
    
    return <span>{text}</span>;
  };

  // 处理工具调用结果的显示
  const renderToolContent = (content: string) => {
    try {
      const toolData = JSON.parse(content);
      if (toolData.tool_name && toolData.result) {
        return (
          <ToolResultSummary
            result={toolData.result}
            isSuccess={!content.includes('错误') && !content.includes('失败')}
            executionTime={toolData.execution_time}
          />
        );
      }
    } catch (e) {
      // 如果不是JSON格式，检查是否是简单的工具结果
      if (content.includes('工具执行') || content.includes('调用工具')) {
        return (
          <ToolContentDisplay
            content={content}
            type="output"
            title="工具执行结果"
            maxLines={6}
          />
        );
      }
    }
    return <div className="whitespace-pre-wrap">{renderImageIfUrl(content)}</div>;
  };

  // 处理助手消息中的工具调用
  const renderAssistantContent = (content: string) => {
    // 检查是否包含工具调用信息
    if (content.includes('调用工具:') || (content.includes('```json') && content.includes('tool_calls'))) {
      const toolSteps = parseToolCallSteps(content);

      if (toolSteps.length > 0) {
        // 分离工具调用部分和普通文本部分
        const toolCallPattern = /调用工具:[^]*?(?=\n\n(?!参数:|结果:)|$)/g;
        const textParts = content.split(toolCallPattern).filter(part => part.trim());

        return (
          <div className="space-y-3">
            {/* 工具调用可视化 */}
            <ToolCallVisualization
              steps={toolSteps}
              isLoading={false}
            />

            {/* 其他文本内容 */}
            {textParts.map((part, index) => (
              part.trim() && !part.includes('调用工具:') ? (
                <div key={index} className="whitespace-pre-wrap">
                  {renderImageIfUrl(part.trim())}
                </div>
              ) : null
            ))}
          </div>
        );
      }

      // 处理旧格式的JSON工具调用
      if (content.includes('```json') && content.includes('tool_calls')) {
        const parts = content.split('```');
        return (
          <div className="space-y-2">
            {parts.map((part, index) => {
              if (part.startsWith('json') && part.includes('tool_calls')) {
                try {
                  const jsonContent = part.replace('json\n', '').trim();
                  const toolCall = JSON.parse(jsonContent);
                  return (
                    <ToolContentDisplay
                      key={index}
                      content={toolCall.tool_calls?.[0]?.function?.arguments || {}}
                      type="input"
                      title={`调用工具: ${toolCall.tool_calls?.[0]?.function?.name}`}
                      maxLines={4}
                    />
                  );
                } catch (e) {
                  return <div key={index} className="whitespace-pre-wrap">{part}</div>;
                }
              }
              return part.trim() ? <div key={index} className="whitespace-pre-wrap">{renderImageIfUrl(part)}</div> : null;
            })}
          </div>
        );
      }
    }

    return <div className="whitespace-pre-wrap">{renderImageIfUrl(content)}</div>;
  };

  // 格式化时间（毫秒转秒）
  const formatDuration = (nanoseconds?: number) => {
    if (!nanoseconds) return null;
    const seconds = (nanoseconds / 1000000000).toFixed(2);
    return `${seconds}s`;
  };

  const renderGenerationStatsIcon = () => {
    // 根据是否有统计数据显示不同的悬浮内容
    const statsText = message.total_duration 
       ? `生成时间: ${(message.total_duration / 1000000).toFixed(2)}ms\n` +
         `提示词处理: ${message.prompt_eval_count || 0} tokens\n` +
         `生成内容: ${message.eval_count || 0} tokens\n` +
         `提示词速度: ${message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0} tokens/s\n` +
         `生成速度: ${message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0} tokens/s`
       : '正在生成中，统计信息将在完成后显示...';

    return (
      <div className="relative group">
        <Info className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
        <div className="absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max">
          {statsText}
        </div>
      </div>
    );
  };

  // 渲染生成统计信息
  const renderGenerationStats = () => {
    if (!isAssistant || !message.total_duration) return null;

    return (
      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1">
        <div className="flex flex-wrap gap-4">
          <span>总时长: {formatDuration(message.total_duration)}</span>
          <span>加载时长: {formatDuration(message.load_duration)}</span>
        </div>
        <div className="flex flex-wrap gap-4">
          <span>提示评估: {message.prompt_eval_count} tokens ({formatDuration(message.prompt_eval_duration)})</span>
          <span>生成: {message.eval_count} tokens ({formatDuration(message.eval_duration)})</span>
        </div>
      </div>
    );
  };

  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="flex flex-col items-start">
          <div className="flex items-start gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
              isTool ? 'bg-orange-600' : 'bg-blue-600'
            }`}>
              {isTool ? (
                <Wrench className="w-4 h-4 text-white" />
              ) : (
                <Bot className="w-4 h-4 text-white" />
              )}
            </div>
            {/* AI状态指示器显示在头像右侧 */}
            {showAIStatus && aiState && aiState.status === 'loading' && (
              <div className="mt-1">
                <AIStatusIndicator aiState={aiState} />
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="flex flex-col max-w-[70%]">
        {/* 模型名称和统计图标显示在消息气泡上方 */}
        {!isUser && isAssistant && (
          <div className="flex items-center gap-2 mb-1">
            {/* 模型名称显示，如果没有则显示加载中 */}
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {message.model || '加载中...'}
            </span>
            {/* 统计信息图标始终显示，但悬浮内容根据数据可用性决定 */}
            {renderGenerationStatsIcon()}
          </div>
        )}
        <div className={`rounded-lg px-4 py-2 ${
        isUser
          ? 'bg-blue-600 text-white'
          : isTool
          ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700'
          : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'
      }`}>
          {/* 工具调用loading状态显示在消息气泡内 */}
          {showAIStatus && aiState && aiState.status === 'tool_calling' && (
            <div className="mb-3">
              <ToolLoadingAnimation
                toolName={aiState.toolName}
                variant="default"
              />
            </div>
          )}
          
          {isTool ? renderToolContent(message.content) : 
           isAssistant ? renderAssistantContent(message.content) :
           <div className="whitespace-pre-wrap">{renderImageIfUrl(message.content)}</div>}
        </div>
      </div>
      
      {isUser && (
        <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <User className="w-4 h-4 text-white" />
        </div>
      )}
    </div>
  );
}