/**
 * MCP服务器实现
 * 使用官方TypeScript SDK创建MCP服务器，提供工具给Ollama模型使用
 */
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
/**
 * 创建并配置MCP服务器
 */
function createMcpServer() {
    const server = new McpServer({
        name: 'kun-agent-tools',
        version: '1.0.0'
    });
    // 注册计算器工具
    server.tool('calculate', {
        expression: z.string().describe('要计算的数学表达式，支持基本的四则运算')
    }, async ({ expression }) => {
        try {
            if (!expression) {
                throw new Error('表达式不能为空');
            }
            // 简单的数学表达式计算（安全起见，只支持基本运算）
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
            const result = Function('"use strict"; return (' + sanitized + ')')();
            return {
                content: [{
                        type: 'text',
                        text: `计算结果: ${expression} = ${result}`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `计算错误: ${error instanceof Error ? error.message : '未知错误'}`
                    }]
            };
        }
    });
    // 注册获取当前时间工具
    server.tool('get_current_time', {}, async () => {
        try {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            return {
                content: [{
                        type: 'text',
                        text: `当前时间: ${timeString}`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `获取时间失败: ${error instanceof Error ? error.message : '未知错误'}`
                    }]
            };
        }
    });
    return server;
}
/**
 * 启动MCP服务器（用于独立运行）
 */
async function startMcpServer() {
    const server = createMcpServer();
    const transport = new StdioServerTransport();
    console.error('MCP服务器启动中...');
    await server.connect(transport);
    console.error('MCP服务器已启动，等待连接...');
}
// 导出函数
export { createMcpServer, startMcpServer };
// 如果直接运行此文件，启动服务器
if (process.argv[1] && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
    startMcpServer().catch(console.error);
}
