'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Cog, <PERSON><PERSON><PERSON><PERSON>, XCir<PERSON>, Clock } from 'lucide-react';

interface ToolCallStep {
  id: string;
  type: 'execution' | 'result';
  toolName: string;
  status: 'pending' | 'running' | 'success' | 'error';
  timestamp?: Date;
  duration?: number;
  input?: Record<string, any>;
  output?: string | Record<string, any>;
  error?: string;
}

interface ToolCallVisualizationProps {
  steps: ToolCallStep[];
  isLoading?: boolean;
  currentToolName?: string;
}

export function ToolCallVisualization({ 
  steps, 
  isLoading = false, 
  currentToolName 
}: ToolCallVisualizationProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());

  const toggleStepExpansion = (stepId: string) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedSteps(newExpanded);
  };

  const getStatusIcon = (status: ToolCallStep['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'running':
        return (
          <div className="w-4 h-4 relative">
            <div className="absolute inset-0 border-2 border-blue-200 rounded-full"></div>
            <div className="absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
          </div>
        );
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: ToolCallStep['status']) => {
    switch (status) {
      case 'pending':
        return 'border-gray-200 bg-gray-50';
      case 'running':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatContent = (content: string | Record<string, any>) => {
    if (typeof content === 'string') {
      return content;
    }
    return JSON.stringify(content, null, 2);
  };

  const truncateContent = (content: string, maxLines: number = 6) => {
    const lines = content.split('\n');
    if (lines.length <= maxLines) {
      return content;
    }
    return lines.slice(0, maxLines).join('\n') + '\n...';
  };

  return (
    <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Cog className="w-4 h-4 text-blue-600" />
          <span className="font-medium text-sm">
            工具调用 {isLoading && currentToolName && `(正在执行: ${currentToolName})`}
          </span>
          {steps.length > 0 && (
            <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded-full">
              {steps.length} 步骤
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </div>

      {/* 内容区域 */}
      {isExpanded && (
        <div className="max-h-96 overflow-y-auto">
          {/* 当前正在执行的工具 */}
          {isLoading && currentToolName && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div className="w-5 h-5 relative">
                  <div className="absolute inset-0 border-2 border-blue-200 rounded-full"></div>
                  <div className="absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
                </div>
                <div>
                  <div className="font-medium text-sm text-blue-800 dark:text-blue-200">
                    正在执行: {currentToolName}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-300">
                    请稍候...
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 步骤列表 */}
          {steps.length > 0 ? (
            <div className="divide-y divide-gray-200 dark:divide-gray-600">
              {steps.map((step, index) => (
                <div key={step.id} className="p-3">
                  <div 
                    className={`border rounded-lg p-3 ${getStatusColor(step.status)} dark:bg-gray-800 dark:border-gray-600 cursor-pointer`}
                    onClick={() => toggleStepExpansion(step.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(step.status)}
                        <div>
                          <div className="font-medium text-sm">
                            {step.type === 'execution' ? '执行工具' : '工具结果'}: {step.toolName}
                          </div>
                          {step.duration && (
                            <div className="text-xs text-gray-500">
                              耗时: {step.duration}ms
                            </div>
                          )}
                        </div>
                      </div>
                      {(step.input || step.output || step.error) && (
                        expandedSteps.has(step.id) ? (
                          <ChevronUp className="w-4 h-4 text-gray-400" />
                        ) : (
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        )
                      )}
                    </div>

                    {/* 展开的内容 */}
                    {expandedSteps.has(step.id) && (
                      <div className="mt-3 space-y-2">
                        {step.input && (
                          <div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              输入参数:
                            </div>
                            <pre className="text-xs bg-white dark:bg-gray-900 p-2 rounded border overflow-x-auto">
                              {formatContent(step.input)}
                            </pre>
                          </div>
                        )}
                        
                        {step.output && (
                          <div>
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              输出结果:
                            </div>
                            <div className="text-xs bg-white dark:bg-gray-900 p-2 rounded border max-h-32 overflow-y-auto">
                              <pre className="whitespace-pre-wrap">
                                {truncateContent(formatContent(step.output))}
                              </pre>
                            </div>
                          </div>
                        )}
                        
                        {step.error && (
                          <div>
                            <div className="text-xs font-medium text-red-600 dark:text-red-400 mb-1">
                              错误信息:
                            </div>
                            <div className="text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-2 rounded border border-red-200 dark:border-red-700">
                              {step.error}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : !isLoading && (
            <div className="p-6 text-center text-gray-500 dark:text-gray-400">
              <Cog className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">暂无工具调用记录</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
