// Ollama API 客户端
const OLLAMA_BASE_URL = 'http://localhost:11434';
export class OllamaClient {
    constructor(baseUrl = OLLAMA_BASE_URL) {
        this.baseUrl = baseUrl;
    }
    /**
     * 获取本地可用的模型列表
     */
    async getModels() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }
            const data = await response.json();
            return data.models || [];
        }
        catch (error) {
            console.error('获取模型列表失败:', error);
            throw new Error('无法连接到Ollama服务，请确保Ollama正在运行');
        }
    }
    /**
     * 发送聊天请求（非流式）
     */
    async chat(request) {
        try {
            const response = await fetch(`${this.baseUrl}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...request,
                    stream: false,
                }),
            });
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error('聊天请求失败:', error);
            throw new Error('聊天请求失败，请检查网络连接和Ollama服务状态');
        }
    }
    /**
     * 发送流式聊天请求
     */
    async *chatStream(request) {
        try {
            // console.log('Ollama chatStream 请求:', JSON.stringify(request, null, 2));
            const response = await fetch(`${this.baseUrl}/api/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ...request,
                    stream: true,
                }),
            });
            // console.log('Ollama 响应状态:', response.status, response.statusText);
            if (!response.ok) {
                const errorText = await response.text();
                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }
            if (!response.body) {
                throw new Error('响应体为空');
            }
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        break;
                    }
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    // 保留最后一行（可能不完整）
                    buffer = lines.pop() || '';
                    for (const line of lines) {
                        const trimmedLine = line.trim();
                        if (trimmedLine) {
                            try {
                                const data = JSON.parse(trimmedLine);
                                yield data;
                                // 如果收到完成标志，结束生成
                                if (data.done) {
                                    return;
                                }
                            }
                            catch (parseError) {
                                console.warn('解析JSON失败:', parseError, '原始数据:', trimmedLine);
                            }
                        }
                    }
                }
                // 处理缓冲区中剩余的数据
                if (buffer.trim()) {
                    try {
                        const data = JSON.parse(buffer.trim());
                        yield data;
                    }
                    catch (parseError) {
                        console.warn('解析最后的JSON失败:', parseError);
                    }
                }
            }
            finally {
                reader.releaseLock();
            }
        }
        catch (error) {
            console.error('流式聊天请求失败:', error);
            if (error instanceof Error) {
                throw error; // 保持原始错误信息
            }
            else {
                throw new Error('流式聊天请求失败，请检查网络连接和Ollama服务状态');
            }
        }
    }
    /**
     * 检查Ollama服务是否可用
     */
    async isAvailable() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`, {
                method: 'GET',
                signal: AbortSignal.timeout(5000), // 5秒超时
            });
            return response.ok;
        }
        catch {
            return false;
        }
    }
    /**
     * 格式化模型大小
     */
    static formatModelSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
    /**
     * 格式化模型名称（移除标签）
     */
    static formatModelName(name) {
        return name.split(':')[0];
    }
}
// 默认客户端实例
export const ollamaClient = new OllamaClient();
