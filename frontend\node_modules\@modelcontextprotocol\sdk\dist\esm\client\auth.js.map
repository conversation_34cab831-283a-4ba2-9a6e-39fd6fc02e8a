{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../src/client/auth.ts"], "names": [], "mappings": "AAAA,OAAO,aAAa,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,aAAa,CAAC;AAEtD,OAAO,EAAE,gCAAgC,EAAE,mBAAmB,EAAE,oCAAoC,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AA0EnJ,MAAM,OAAO,iBAAkB,SAAQ,KAAK;IAC1C,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,cAAc,CAAC,CAAC;IACnC,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,IAAI,CACxB,QAA6B,EAC7B,EAAE,SAAS,EACT,iBAAiB,EACjB,KAAK,EACL,mBAAmB,EAKQ;IAE7B,IAAI,sBAAsB,GAAG,SAAS,CAAC;IACvC,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,MAAM,sCAAsC,CACnE,mBAAmB,IAAI,SAAS,CAAC,CAAC;QAEpC,IAAI,gBAAgB,CAAC,qBAAqB,IAAI,gBAAgB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChG,sBAAsB,GAAG,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,2GAA2G,EAAE,KAAK,CAAC,CAAA;IAClI,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,qBAAqB,CAAC,sBAAsB,CAAC,CAAC;IAErE,uCAAuC;IACvC,IAAI,iBAAiB,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC5E,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,qFAAqF,CAAC,CAAC;QACzG,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,cAAc,CAAC,sBAAsB,EAAE;YACnE,QAAQ;YACR,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC,CAAC;QAEH,MAAM,QAAQ,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QACtD,iBAAiB,GAAG,eAAe,CAAC;IACtC,CAAC;IAED,yCAAyC;IACzC,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,YAAY,EAAE,CAAC;QACnD,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,sBAAsB,EAAE;YACjE,QAAQ;YACR,iBAAiB;YACjB,iBAAiB;YACjB,YAAY;YACZ,WAAW,EAAE,QAAQ,CAAC,WAAW;SAClC,CAAC,CAAC;QAEH,MAAM,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAClC,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,MAAM,EAAE,CAAC;IAEvC,4CAA4C;IAC5C,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,SAAS,GAAG,MAAM,oBAAoB,CAAC,sBAAsB,EAAE;gBACnE,QAAQ;gBACR,iBAAiB;gBACjB,YAAY,EAAE,MAAM,CAAC,aAAa;aACnC,CAAC,CAAC;YAEH,MAAM,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAElE,+BAA+B;IAC/B,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,MAAM,kBAAkB,CAAC,sBAAsB,EAAE;QAC1F,QAAQ;QACR,iBAAiB;QACjB,KAAK;QACL,WAAW,EAAE,QAAQ,CAAC,WAAW;QACjC,KAAK,EAAE,KAAK,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAK;KAC9C,CAAC,CAAC;IAEH,MAAM,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAC9C,MAAM,QAAQ,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;IACzD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,0BAA0B,CAAC,GAAa;IAEtD,MAAM,kBAAkB,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC/D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACrD,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,MAAM,KAAK,GAAG,6BAA6B,CAAC;IAC5C,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,CAAC;QACH,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAAC,WAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,sCAAsC,CAC1D,SAAuB,EACvB,IAAuE;;IAGvE,IAAI,GAAQ,CAAA;IACZ,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,mBAAmB,EAAE,CAAC;QAC9B,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,mBAAmB,CAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,GAAG,GAAG,IAAI,GAAG,CAAC,uCAAuC,EAAE,SAAS,CAAC,CAAC;IACpE,CAAC;IAED,IAAI,QAAkB,CAAC;IACvB,IAAI,CAAC;QACH,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAC1B,OAAO,EAAE;gBACP,sBAAsB,EAAE,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,mCAAI,uBAAuB;aACzE;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qCAAqC;QACrC,IAAI,KAAK,YAAY,SAAS,EAAE,CAAC;YAC/B,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;IAC/F,CAAC;IAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CACb,QAAQ,QAAQ,CAAC,MAAM,+DAA+D,CACvF,CAAC;IACJ,CAAC;IACD,OAAO,oCAAoC,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,sBAAoC,EACpC,IAAmC;;IAEnC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,yCAAyC,EAAE,sBAAsB,CAAC,CAAC;IACvF,IAAI,QAAkB,CAAC;IACvB,IAAI,CAAC;QACH,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;YAC1B,OAAO,EAAE;gBACP,sBAAsB,EAAE,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,mCAAI,uBAAuB;aACzE;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,qCAAqC;QACrC,IAAI,KAAK,YAAY,SAAS,EAAE,CAAC;YAC/B,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QAC5B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CACb,QAAQ,QAAQ,CAAC,MAAM,2CAA2C,CACnE,CAAC;IACJ,CAAC;IAED,OAAO,mBAAmB,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,sBAAoC,EACpC,EACE,QAAQ,EACR,iBAAiB,EACjB,WAAW,EACX,KAAK,EACL,KAAK,GAON;IAED,MAAM,YAAY,GAAG,MAAM,CAAC;IAC5B,MAAM,mBAAmB,GAAG,MAAM,CAAC;IAEnC,IAAI,gBAAqB,CAAC;IAC1B,IAAI,QAAQ,EAAE,CAAC;QACb,gBAAgB,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;QAE5D,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,KAAK,CACb,4DAA4D,YAAY,EAAE,CAC3E,CAAC;QACJ,CAAC;QAED,IACE,CAAC,QAAQ,CAAC,gCAAgC;YAC1C,CAAC,QAAQ,CAAC,gCAAgC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EACxE,CAAC;YACD,MAAM,IAAI,KAAK,CACb,oEAAoE,mBAAmB,EAAE,CAC1F,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,gBAAgB,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;IACnE,CAAC;IAED,0BAA0B;IAC1B,MAAM,SAAS,GAAG,MAAM,aAAa,EAAE,CAAC;IACxC,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC;IAC7C,MAAM,aAAa,GAAG,SAAS,CAAC,cAAc,CAAC;IAE/C,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;IACjE,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC5E,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IACnE,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAC/B,uBAAuB,EACvB,mBAAmB,CACpB,CAAC;IACF,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;IAEvE,IAAI,KAAK,EAAE,CAAC;QACV,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACV,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,sBAAoC,EACpC,EACE,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,WAAW,GAOZ;IAED,MAAM,SAAS,GAAG,oBAAoB,CAAC;IAEvC,IAAI,QAAa,CAAC;IAClB,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAE5C,IACE,QAAQ,CAAC,qBAAqB;YAC9B,CAAC,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,SAAS,CAAC,EACnD,CAAC;YACD,MAAM,IAAI,KAAK,CACb,yDAAyD,SAAS,EAAE,CACrE,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IACvD,CAAC;IAED,2BAA2B;IAC3B,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;QACjC,UAAU,EAAE,SAAS;QACrB,SAAS,EAAE,iBAAiB,CAAC,SAAS;QACtC,IAAI,EAAE,iBAAiB;QACvB,aAAa,EAAE,YAAY;QAC3B,YAAY,EAAE,MAAM,CAAC,WAAW,CAAC;KAClC,CAAC,CAAC;IAEH,IAAI,iBAAiB,CAAC,aAAa,EAAE,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE;QACrC,MAAM,EAAE,MAAM;QACd,OAAO,EAAE;YACP,cAAc,EAAE,mCAAmC;SACpD;QACD,IAAI,EAAE,MAAM;KACb,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,OAAO,iBAAiB,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AACxD,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,sBAAoC,EACpC,EACE,QAAQ,EACR,iBAAiB,EACjB,YAAY,GAKb;IAED,MAAM,SAAS,GAAG,eAAe,CAAC;IAElC,IAAI,QAAa,CAAC;IAClB,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAE5C,IACE,QAAQ,CAAC,qBAAqB;YAC9B,CAAC,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,SAAS,CAAC,EACnD,CAAC;YACD,MAAM,IAAI,KAAK,CACb,yDAAyD,SAAS,EAAE,CACrE,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,QAAQ,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IACvD,CAAC;IAED,yBAAyB;IACzB,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;QACjC,UAAU,EAAE,SAAS;QACrB,SAAS,EAAE,iBAAiB,CAAC,SAAS;QACtC,aAAa,EAAE,YAAY;KAC5B,CAAC,CAAC;IAEH,IAAI,iBAAiB,CAAC,aAAa,EAAE,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,QAAQ,EAAE;QACrC,MAAM,EAAE,MAAM;QACd,OAAO,EAAE;YACP,cAAc,EAAE,mCAAmC;SACpD;QACD,IAAI,EAAE,MAAM;KACb,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,iBAAiB,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9F,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,sBAAoC,EACpC,EACE,QAAQ,EACR,cAAc,GAIf;IAED,IAAI,eAAoB,CAAC;IAEzB,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;QAC5F,CAAC;QAED,eAAe,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;IAC5D,CAAC;SAAM,CAAC;QACN,eAAe,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;IACjE,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,eAAe,EAAE;QAC5C,MAAM,EAAE,MAAM;QACd,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;SACnC;QACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;KACrC,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,4CAA4C,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,gCAAgC,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC"}