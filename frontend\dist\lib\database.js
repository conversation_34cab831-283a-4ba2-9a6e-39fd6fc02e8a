import Database from 'better-sqlite3';
import path from 'path';
const dbPath = path.join(process.cwd(), 'chat.db');
const db = new Database(dbPath);
// 初始化数据库表
db.exec(`
  CREATE TABLE IF NOT EXISTS conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    model TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  );

  CREATE TABLE IF NOT EXISTS messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
  );

  -- MCP服务器统一配置表
  CREATE TABLE IF NOT EXISTS mcp_servers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),
    status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),
    enabled BOOLEAN NOT NULL DEFAULT 1,
    
    -- STDIO配置
    command TEXT,
    args TEXT, -- JSON数组格式
    working_directory TEXT,
    
    -- SSE/HTTP配置
    url TEXT,
    base_url TEXT,
    port INTEGER,
    path TEXT DEFAULT '/',
    protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),
    
    -- 通用配置
    headers TEXT, -- JSON对象格式
    auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),
    auth_config TEXT, -- JSON格式
    timeout_ms INTEGER DEFAULT 30000,
    retry_attempts INTEGER DEFAULT 3,
    retry_delay_ms INTEGER DEFAULT 1000,
    
    -- 扩展配置
    extra_config TEXT, -- JSON格式，存储其他特殊配置
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_connected_at DATETIME,
    error_message TEXT
  );

  -- MCP工具表
  CREATE TABLE IF NOT EXISTS mcp_tools (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    server_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    input_schema TEXT, -- JSON格式存储工具的输入参数模式
    is_available BOOLEAN DEFAULT 1,
    enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）
    last_used_at DATETIME,
    usage_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,
    UNIQUE(server_id, name)
  );

  -- MCP工具调用记录表
  CREATE TABLE IF NOT EXISTS mcp_tool_calls (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tool_id INTEGER NOT NULL,
    conversation_id INTEGER NOT NULL, -- 关联到对话
    message_id INTEGER, -- 关联到具体的消息（可选）
    input_args TEXT, -- JSON格式存储输入参数
    output_result TEXT, -- JSON格式存储输出结果
    execution_time_ms INTEGER,
    status TEXT NOT NULL CHECK (status IN ('success', 'error', 'timeout')),
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,
    FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE SET NULL
  );

  -- 原有索引
  CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
  CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
  
  -- MCP相关索引
  CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);
  CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);
  CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);
  CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);
  CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);
  CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conversation_id ON mcp_tool_calls(conversation_id);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_id ON mcp_tool_calls(tool_id);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_message_id ON mcp_tool_calls(message_id);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at DESC);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_time ON mcp_tool_calls(conversation_id, created_at DESC);
  CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_status ON mcp_tool_calls(conversation_id, status);
`);
// 对话相关操作
export const conversationQueries = {
    // 创建新对话
    create: db.prepare(`
    INSERT INTO conversations (title, model)
    VALUES (?, ?)
  `),
    // 获取所有对话
    getAll: db.prepare(`
    SELECT * FROM conversations
    ORDER BY updated_at DESC
  `),
    // 根据ID获取对话
    getById: db.prepare(`
    SELECT * FROM conversations
    WHERE id = ?
  `),
    // 更新对话标题
    updateTitle: db.prepare(`
    UPDATE conversations
    SET title = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
    // 更新对话的最后更新时间
    updateTimestamp: db.prepare(`
    UPDATE conversations
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
    // 删除对话
    delete: db.prepare(`
    DELETE FROM conversations
    WHERE id = ?
  `),
};
// 消息相关操作
export const messageQueries = {
    // 创建新消息
    create: db.prepare(`
    INSERT INTO messages (conversation_id, role, content)
    VALUES (?, ?, ?)
  `),
    // 获取对话的所有消息
    getByConversationId: db.prepare(`
    SELECT * FROM messages
    WHERE conversation_id = ?
    ORDER BY created_at ASC
  `),
    // 删除对话的所有消息
    deleteByConversationId: db.prepare(`
    DELETE FROM messages
    WHERE conversation_id = ?
  `),
};
// 数据库操作函数
export const dbOperations = {
    // 创建新对话
    createConversation(data) {
        const result = conversationQueries.create.run(data.title, data.model);
        return result.lastInsertRowid;
    },
    // 获取所有对话
    getAllConversations() {
        return conversationQueries.getAll.all();
    },
    // 根据ID获取对话
    getConversationById(id) {
        return conversationQueries.getById.get(id);
    },
    // 更新对话标题
    updateConversationTitle(id, title) {
        conversationQueries.updateTitle.run(title, id);
    },
    // 更新对话时间戳
    updateConversationTimestamp(id) {
        conversationQueries.updateTimestamp.run(id);
    },
    // 删除对话
    deleteConversation(id) {
        conversationQueries.delete.run(id);
    },
    // 创建新消息
    createMessage(data) {
        const result = messageQueries.create.run(data.conversation_id, data.role, data.content);
        // 更新对话的时间戳
        this.updateConversationTimestamp(data.conversation_id);
        return result.lastInsertRowid;
    },
    // 获取对话的所有消息
    getMessagesByConversationId(conversationId) {
        return messageQueries.getByConversationId.all(conversationId);
    },
    // 删除对话的所有消息
    deleteMessagesByConversationId(conversationId) {
        messageQueries.deleteByConversationId.run(conversationId);
    },
};
// MCP服务器相关操作
export const mcpServerQueries = {
    // 创建MCP服务器
    create: db.prepare(`
    INSERT INTO mcp_servers (
      name, display_name, description, type, enabled,
      command, args, working_directory,
      url, base_url, port, path, protocol,
      headers, auth_type, auth_config, timeout_ms, retry_attempts, retry_delay_ms,
      extra_config
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),
    // 获取所有MCP服务器
    getAll: db.prepare(`
    SELECT * FROM mcp_servers
    ORDER BY created_at DESC
  `),
    // 根据ID获取MCP服务器
    getById: db.prepare(`
    SELECT * FROM mcp_servers
    WHERE id = ?
  `),
    // 根据名称获取MCP服务器
    getByName: db.prepare(`
    SELECT * FROM mcp_servers
    WHERE name = ?
  `),
    // 获取启用的MCP服务器
    getEnabled: db.prepare(`
    SELECT * FROM mcp_servers
    WHERE enabled = 1
    ORDER BY created_at DESC
  `),
    // 更新MCP服务器状态
    updateStatus: db.prepare(`
    UPDATE mcp_servers
    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP,
        last_connected_at = CASE WHEN ? = 'connected' THEN CURRENT_TIMESTAMP ELSE last_connected_at END
    WHERE id = ?
  `),
    // 更新MCP服务器配置
    update: db.prepare(`
    UPDATE mcp_servers
    SET display_name = ?, description = ?, type = ?, enabled = ?,
        command = ?, args = ?, working_directory = ?,
        url = ?, base_url = ?, port = ?, path = ?, protocol = ?,
        headers = ?, auth_type = ?, auth_config = ?, timeout_ms = ?, retry_attempts = ?, retry_delay_ms = ?,
        extra_config = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
    // 删除MCP服务器
    delete: db.prepare(`
    DELETE FROM mcp_servers
    WHERE id = ?
  `),
};
// MCP工具相关操作
export const mcpToolQueries = {
    // 创建MCP工具
    create: db.prepare(`
    INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available)
    VALUES (?, ?, ?, ?, ?)
  `),
    // 获取服务器的所有工具
    getByServerId: db.prepare(`
    SELECT * FROM mcp_tools
    WHERE server_id = ?
    ORDER BY name ASC
  `),
    // 根据ID获取工具
    getById: db.prepare(`
    SELECT * FROM mcp_tools
    WHERE id = ?
  `),
    // 根据服务器ID和工具名称获取工具
    getByServerIdAndName: db.prepare(`
    SELECT * FROM mcp_tools
    WHERE server_id = ? AND name = ?
  `),
    // 获取可用的工具
    getAvailable: db.prepare(`
    SELECT t.*, s.name as server_name, s.status as server_status
    FROM mcp_tools t
    JOIN mcp_servers s ON t.server_id = s.id
    WHERE t.is_available = 1 AND s.enabled = 1 AND s.status = 'connected'
    ORDER BY t.name ASC
  `),
    // 更新工具使用统计
    updateUsage: db.prepare(`
    UPDATE mcp_tools
    SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
    // 更新工具可用性
    updateAvailability: db.prepare(`
    UPDATE mcp_tools
    SET is_available = ?, updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `),
    // 删除服务器的所有工具
    deleteByServerId: db.prepare(`
    DELETE FROM mcp_tools
    WHERE server_id = ?
  `),
    // 删除工具
    delete: db.prepare(`
    DELETE FROM mcp_tools
    WHERE id = ?
  `),
};
// MCP工具调用相关操作
export const mcpToolCallQueries = {
    // 创建工具调用记录
    create: db.prepare(`
    INSERT INTO mcp_tool_calls (
      tool_id, conversation_id, message_id, input_args, output_result,
      execution_time_ms, status, error_message
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `),
    // 获取对话的工具调用记录
    getByConversationId: db.prepare(`
    SELECT tc.*, t.name as tool_name, s.name as server_name
    FROM mcp_tool_calls tc
    JOIN mcp_tools t ON tc.tool_id = t.id
    JOIN mcp_servers s ON t.server_id = s.id
    WHERE tc.conversation_id = ?
    ORDER BY tc.created_at DESC
  `),
    // 获取工具的调用记录
    getByToolId: db.prepare(`
    SELECT * FROM mcp_tool_calls
    WHERE tool_id = ?
    ORDER BY created_at DESC
  `),
    // 获取最近的工具调用记录
    getRecent: db.prepare(`
    SELECT tc.*, t.name as tool_name, s.name as server_name
    FROM mcp_tool_calls tc
    JOIN mcp_tools t ON tc.tool_id = t.id
    JOIN mcp_servers s ON t.server_id = s.id
    ORDER BY tc.created_at DESC
    LIMIT ?
  `),
    // 删除对话的工具调用记录
    deleteByConversationId: db.prepare(`
    DELETE FROM mcp_tool_calls
    WHERE conversation_id = ?
  `),
};
// MCP数据库操作函数
export const mcpDbOperations = {
    // 创建MCP服务器
    createMcpServer(data) {
        const result = mcpServerQueries.create.run(data.name, data.display_name, data.description || null, data.type, Boolean(data.enabled ?? true) ? 1 : 0, // 确保布尔值转换为数字
        data.command || null, data.args ? JSON.stringify(data.args) : null, data.working_directory || null, data.url || null, data.base_url || null, data.port ? Number(data.port) : null, data.path || null, data.protocol || null, data.headers ? JSON.stringify(data.headers) : null, data.auth_type || null, data.auth_config ? JSON.stringify(data.auth_config) : null, data.timeout_ms ? Number(data.timeout_ms) : null, data.retry_attempts ? Number(data.retry_attempts) : null, data.retry_delay_ms ? Number(data.retry_delay_ms) : null, data.extra_config ? JSON.stringify(data.extra_config) : null);
        return result.lastInsertRowid;
    },
    // 获取所有MCP服务器
    getAllMcpServers() {
        return mcpServerQueries.getAll.all();
    },
    // 根据ID获取MCP服务器
    getMcpServerById(id) {
        return mcpServerQueries.getById.get(id);
    },
    // 根据名称获取MCP服务器
    getMcpServerByName(name) {
        return mcpServerQueries.getByName.get(name);
    },
    // 获取启用的MCP服务器
    getEnabledMcpServers() {
        return mcpServerQueries.getEnabled.all();
    },
    // 更新MCP服务器状态
    updateMcpServerStatus(id, status, errorMessage) {
        mcpServerQueries.updateStatus.run(status, errorMessage || null, status, id);
    },
    // 删除MCP服务器
    deleteMcpServer(id) {
        mcpServerQueries.delete.run(id);
    },
    // 创建MCP工具
    createMcpTool(data) {
        const result = mcpToolQueries.create.run(data.server_id, data.name, data.description || null, data.input_schema ? JSON.stringify(data.input_schema) : null, Boolean(data.is_available ?? true) ? 1 : 0 // 确保布尔值转换为数字
        );
        return result.lastInsertRowid;
    },
    // 获取服务器的所有工具
    getMcpToolsByServerId(serverId) {
        return mcpToolQueries.getByServerId.all(serverId);
    },
    // 获取可用的工具
    getAvailableMcpTools() {
        return mcpToolQueries.getAvailable.all();
    },
    // 更新工具使用统计
    updateMcpToolUsage(toolId) {
        mcpToolQueries.updateUsage.run(toolId);
    },
    // 删除服务器的所有工具
    deleteMcpToolsByServerId(serverId) {
        mcpToolQueries.deleteByServerId.run(serverId);
    },
    // 创建工具调用记录
    createMcpToolCall(data) {
        const result = mcpToolCallQueries.create.run(data.tool_id, data.conversation_id, data.message_id || null, data.input_args ? JSON.stringify(data.input_args) : null, data.output_result ? JSON.stringify(data.output_result) : null, data.execution_time_ms || null, data.status, data.error_message || null);
        // 更新工具使用统计
        if (data.status === 'success') {
            this.updateMcpToolUsage(data.tool_id);
        }
        return result.lastInsertRowid;
    },
    // 获取对话的工具调用记录
    getMcpToolCallsByConversationId(conversationId) {
        return mcpToolCallQueries.getByConversationId.all(conversationId);
    },
    // 获取最近的工具调用记录
    getRecentMcpToolCalls(limit = 50) {
        return mcpToolCallQueries.getRecent.all(limit);
    },
    // 删除对话的工具调用记录
    deleteMcpToolCallsByConversationId(conversationId) {
        mcpToolCallQueries.deleteByConversationId.run(conversationId);
    },
};
export default db;
