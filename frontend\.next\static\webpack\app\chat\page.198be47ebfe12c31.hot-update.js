"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\nfunction MessageItem(param) {\n    let { message, showAIStatus, aiState } = param;\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    // 解析工具调用内容，创建步骤数据\n    const parseToolCallSteps = (content)=>{\n        const steps = [];\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') && content.includes('参数:') && content.includes('结果:')) {\n            const toolCallMatches = content.match(/调用工具:\\s*([^\\n]+)\\n参数:\\s*([^]*?)\\n\\n结果:\\s*([^]*?)(?=\\n\\n|$)/g);\n            if (toolCallMatches) {\n                toolCallMatches.forEach((match, index)=>{\n                    const toolNameMatch = match.match(/调用工具:\\s*([^\\n]+)/);\n                    const paramsMatch = match.match(/参数:\\s*([^]*?)\\n\\n结果:/);\n                    const resultMatch = match.match(/结果:\\s*([^]*?)(?=\\n\\n|$)/);\n                    if (toolNameMatch) {\n                        const toolName = toolNameMatch[1].trim();\n                        let params = {};\n                        let result = '';\n                        try {\n                            if (paramsMatch) {\n                                params = JSON.parse(paramsMatch[1].trim());\n                            }\n                        } catch (e) {\n                            var _paramsMatch_;\n                            params = {\n                                raw: (paramsMatch === null || paramsMatch === void 0 ? void 0 : (_paramsMatch_ = paramsMatch[1]) === null || _paramsMatch_ === void 0 ? void 0 : _paramsMatch_.trim()) || ''\n                            };\n                        }\n                        if (resultMatch) {\n                            result = resultMatch[1].trim();\n                        }\n                        // 添加执行步骤\n                        steps.push({\n                            id: \"execution-\".concat(index),\n                            type: 'execution',\n                            toolName,\n                            status: 'success',\n                            timestamp: new Date(),\n                            input: params\n                        });\n                        // 添加结果步骤\n                        steps.push({\n                            id: \"result-\".concat(index),\n                            type: 'result',\n                            toolName,\n                            status: result.includes('错误') || result.includes('失败') ? 'error' : 'success',\n                            timestamp: new Date(),\n                            output: result,\n                            error: result.includes('错误') || result.includes('失败') ? result : undefined\n                        });\n                    }\n                });\n            }\n        }\n        return steps;\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 116,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理工具调用结果的显示\n    const renderToolContent = (content)=>{\n        try {\n            const toolData = JSON.parse(content);\n            if (toolData.tool_name && toolData.result) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm\",\n                            children: [\n                                \"\\uD83D\\uDD27 \",\n                                toolData.tool_name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90\",\n                            children: typeof toolData.result === 'string' ? renderImageIfUrl(toolData.result) : renderImageIfUrl(JSON.stringify(toolData.result, null, 2))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this);\n            }\n        } catch (e) {\n        // 如果不是JSON格式，直接显示原内容并检测图片\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息中的工具调用\n    const renderAssistantContent = (content)=>{\n        // 检查是否包含工具调用\n        if (content.includes('```json') && content.includes('tool_calls')) {\n            const parts = content.split('```');\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (part.startsWith('json') && part.includes('tool_calls')) {\n                        try {\n                            var _toolCall_tool_calls__function, _toolCall_tool_calls_, _toolCall_tool_calls, _toolCall_tool_calls__function1, _toolCall_tool_calls_1, _toolCall_tool_calls1;\n                            const jsonContent = part.replace('json\\n', '').trim();\n                            const toolCall = JSON.parse(jsonContent);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded p-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium mb-1\",\n                                        children: [\n                                            \"\\uD83D\\uDD27 调用工具: \",\n                                            (_toolCall_tool_calls = toolCall.tool_calls) === null || _toolCall_tool_calls === void 0 ? void 0 : (_toolCall_tool_calls_ = _toolCall_tool_calls[0]) === null || _toolCall_tool_calls_ === void 0 ? void 0 : (_toolCall_tool_calls__function = _toolCall_tool_calls_.function) === null || _toolCall_tool_calls__function === void 0 ? void 0 : _toolCall_tool_calls__function.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs opacity-75 overflow-x-auto\",\n                                        children: JSON.stringify((_toolCall_tool_calls1 = toolCall.tool_calls) === null || _toolCall_tool_calls1 === void 0 ? void 0 : (_toolCall_tool_calls_1 = _toolCall_tool_calls1[0]) === null || _toolCall_tool_calls_1 === void 0 ? void 0 : (_toolCall_tool_calls__function1 = _toolCall_tool_calls_1.function) === null || _toolCall_tool_calls__function1 === void 0 ? void 0 : _toolCall_tool_calls__function1.arguments, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 19\n                            }, this);\n                        } catch (e) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: part\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 24\n                            }, this);\n                        }\n                    }\n                    return part.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"whitespace-pre-wrap\",\n                        children: renderImageIfUrl(part)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 34\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 173,\n            columnNumber: 12\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 \".concat(isUser ? 'bg-blue-600 text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'),\n                        children: [\n                            showAIStatus && aiState && aiState.status === 'tool_calling' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2 p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner-small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-green-700 dark:text-green-300\",\n                                        children: aiState.toolName ? \"正在调用 \".concat(aiState.toolName, \"...\") : '正在调用工具...'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this),\n                            isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(message.content)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ })

});