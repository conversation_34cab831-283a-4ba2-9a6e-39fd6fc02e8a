import { NextResponse } from 'next/server';
import Database from 'better-sqlite3';
import path from 'path';
const dbPath = path.join(process.cwd(), 'chat.db');
// 从数据库获取服务器列表和工具统计
function getServersFromDatabase() {
    const db = new Database(dbPath);
    try {
        // 获取服务器信息及其工具数量
        const servers = db.prepare(`
      SELECT 
        s.*,
        COUNT(t.id) as tool_count
      FROM mcp_servers s
      LEFT JOIN mcp_tools t ON s.id = t.server_id AND t.is_available = 1
      WHERE s.enabled = 1
      GROUP BY s.id
      ORDER BY s.created_at DESC
    `).all();
        return servers;
    }
    finally {
        db.close();
    }
}
export async function GET() {
    try {
        const servers = [];
        // 添加本地服务器（从数据库获取工具数量）
        try {
            const { mcpServerClient } = require('../../../../lib/mcp/mcp-client-server');
            const isConnected = mcpServerClient.isClientConnected();
            // 从数据库获取本地工具数量（如果有的话）
            const db = new Database(dbPath);
            let localToolCount = 0;
            try {
                const localServer = db.prepare('SELECT id FROM mcp_servers WHERE name = ?').get('local');
                if (localServer) {
                    const toolCountResult = db.prepare('SELECT COUNT(*) as count FROM mcp_tools WHERE server_id = ? AND is_available = 1').get(localServer.id);
                    localToolCount = toolCountResult?.count || 0;
                }
                else {
                    // 如果数据库中没有本地服务器记录，直接从客户端获取
                    const localTools = isConnected ? mcpServerClient.getAvailableTools() : [];
                    localToolCount = localTools.length;
                }
            }
            finally {
                db.close();
            }
            servers.push({
                name: 'local',
                displayName: '本地服务器',
                type: 'stdio',
                status: isConnected ? 'connected' : 'disconnected',
                toolCount: localToolCount,
                description: '本地MCP服务器，提供基础工具功能'
            });
        }
        catch (error) {
            console.error('获取本地服务器信息失败:', error);
            servers.push({
                name: 'local',
                displayName: '本地服务器',
                type: 'stdio',
                status: 'error',
                toolCount: 0,
                description: '本地MCP服务器，提供基础工具功能'
            });
        }
        // 从数据库获取外部服务器信息
        try {
            const dbServers = getServersFromDatabase();
            // 添加数据库中的外部服务器信息
            dbServers.forEach((server) => {
                servers.push({
                    id: server.id,
                    name: server.name,
                    displayName: server.display_name || server.name,
                    type: server.type,
                    status: server.status || 'disconnected', // 使用数据库中存储的状态
                    toolCount: server.tool_count || 0,
                    description: server.description || `外部MCP服务器: ${server.name}`,
                    lastConnectedAt: server.last_connected_at,
                    errorMessage: server.error_message
                });
            });
        }
        catch (error) {
            console.error('获取外部服务器信息失败:', error);
        }
        return NextResponse.json({
            success: true,
            servers: servers
        });
    }
    catch (error) {
        console.error('获取服务器列表失败:', error);
        return NextResponse.json({ error: '获取服务器列表失败' }, { status: 500 });
    }
}
