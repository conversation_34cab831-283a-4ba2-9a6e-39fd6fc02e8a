'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, Co<PERSON>, Check } from 'lucide-react';

interface ToolContentDisplayProps {
  content: string | Record<string, any>;
  maxLines?: number;
  title?: string;
  type?: 'input' | 'output' | 'error';
  showCopyButton?: boolean;
}

export function ToolContentDisplay({ 
  content, 
  maxLines = 6, 
  title,
  type = 'output',
  showCopyButton = true
}: ToolContentDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [needsTruncation, setNeedsTruncation] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const formattedContent = typeof content === 'string' 
    ? content 
    : JSON.stringify(content, null, 2);

  // 检查内容是否需要截断
  useEffect(() => {
    if (contentRef.current) {
      const lines = formattedContent.split('\n');
      setNeedsTruncation(lines.length > maxLines);
    }
  }, [formattedContent, maxLines]);

  const getDisplayContent = () => {
    if (isExpanded || !needsTruncation) {
      return formattedContent;
    }
    
    const lines = formattedContent.split('\n');
    return lines.slice(0, maxLines).join('\n') + '\n...';
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'input':
        return {
          container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700',
          title: 'text-blue-700 dark:text-blue-300',
          content: 'bg-white dark:bg-gray-900'
        };
      case 'error':
        return {
          container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700',
          title: 'text-red-700 dark:text-red-300',
          content: 'bg-red-50 dark:bg-red-900/10 text-red-800 dark:text-red-200'
        };
      default: // output
        return {
          container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700',
          title: 'text-green-700 dark:text-green-300',
          content: 'bg-white dark:bg-gray-900'
        };
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(formattedContent);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const styles = getTypeStyles();

  return (
    <div className={`border rounded-lg ${styles.container}`}>
      {/* 标题栏 */}
      {title && (
        <div className="flex items-center justify-between px-3 py-2 border-b border-current border-opacity-20">
          <span className={`text-sm font-medium ${styles.title}`}>
            {title}
          </span>
          <div className="flex items-center gap-2">
            {showCopyButton && (
              <button
                onClick={handleCopy}
                className={`p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors ${styles.title}`}
                title="复制内容"
              >
                {isCopied ? (
                  <Check className="w-3 h-3" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
              </button>
            )}
            {needsTruncation && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className={`p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors ${styles.title}`}
                title={isExpanded ? "收起" : "展开"}
              >
                {isExpanded ? (
                  <ChevronUp className="w-3 h-3" />
                ) : (
                  <ChevronDown className="w-3 h-3" />
                )}
              </button>
            )}
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="p-3">
        <div 
          ref={contentRef}
          className={`text-sm font-mono rounded border p-3 ${styles.content} ${
            !isExpanded && needsTruncation ? 'max-h-32 overflow-hidden' : 'max-h-96 overflow-y-auto'
          }`}
        >
          <pre className="whitespace-pre-wrap break-words">
            {getDisplayContent()}
          </pre>
        </div>

        {/* 展开/收起按钮 */}
        {needsTruncation && (
          <div className="mt-2 text-center">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`text-xs px-3 py-1 rounded-full border transition-colors hover:bg-black hover:bg-opacity-5 ${styles.title}`}
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="w-3 h-3 inline mr-1" />
                  收起内容
                </>
              ) : (
                <>
                  <ChevronDown className="w-3 h-3 inline mr-1" />
                  显示完整内容 ({formattedContent.split('\n').length - maxLines} 行更多)
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// 简化版本的内容显示组件
export function SimpleToolContent({ 
  content, 
  maxHeight = '8rem' 
}: { 
  content: string | Record<string, any>; 
  maxHeight?: string 
}) {
  const formattedContent = typeof content === 'string' 
    ? content 
    : JSON.stringify(content, null, 2);

  return (
    <div 
      className="text-sm bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded p-3 overflow-y-auto"
      style={{ maxHeight }}
    >
      <pre className="whitespace-pre-wrap break-words text-gray-800 dark:text-gray-200">
        {formattedContent}
      </pre>
    </div>
  );
}

// 工具结果摘要组件
export function ToolResultSummary({ 
  result, 
  isSuccess = true,
  executionTime 
}: { 
  result: string | Record<string, any>; 
  isSuccess?: boolean;
  executionTime?: number;
}) {
  const [showDetails, setShowDetails] = useState(false);
  
  const getSummary = () => {
    const content = typeof result === 'string' ? result : JSON.stringify(result);
    if (content.length <= 100) return content;
    return content.substring(0, 100) + '...';
  };

  return (
    <div className={`border rounded-lg ${
      isSuccess 
        ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700' 
        : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700'
    }`}>
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <span className={`text-sm font-medium ${
            isSuccess ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
          }`}>
            {isSuccess ? '执行成功' : '执行失败'}
          </span>
          {executionTime && (
            <span className="text-xs text-gray-500">
              {executionTime}ms
            </span>
          )}
        </div>
        
        <div className="text-sm text-gray-700 dark:text-gray-300 mb-2">
          {getSummary()}
        </div>
        
        {typeof result === 'string' && result.length > 100 && (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
          >
            {showDetails ? '隐藏详情' : '查看详情'}
          </button>
        )}
      </div>
      
      {showDetails && (
        <div className="border-t border-current border-opacity-20 p-3">
          <SimpleToolContent content={result} maxHeight="12rem" />
        </div>
      )}
    </div>
  );
}
