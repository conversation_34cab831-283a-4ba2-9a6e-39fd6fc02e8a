'use client';

import { useState, useEffect } from 'react';
import { availableTools, getAllAvailableTools } from '@/lib/tools';
import { Tool } from '@/lib/ollama';

interface UseToolSettingsProps {
  selectedModel: string;
  enableTools: boolean;
  selectedTools: string[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: string[]) => void;
}

export function useToolSettings({
  selectedModel,
  enableTools,
  selectedTools,
  onToolsToggle,
  onSelectedToolsChange,
}: UseToolSettingsProps) {
  const [showToolSettings, setShowToolSettings] = useState(false);
  const [modelSupportsTools, setModelSupportsTools] = useState<boolean | null>(null);
  const [isCheckingModel, setIsCheckingModel] = useState(false);
  const [allTools, setAllTools] = useState<Tool[]>(availableTools);

  // 验证模型是否支持工具调用
  const checkModelToolSupport = async (model: string) => {
    if (!model) return;
    
    setIsCheckingModel(true);
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: model,
          messages: [{ role: 'user', content: 'test' }],
          enableTools: true,
          testMode: true, // 添加测试模式标识
        }),
      });
      
      const data = await response.json();
      if (data.success) {
        setModelSupportsTools(data.supportsTools);
      } else {
        setModelSupportsTools(false);
      }
    } catch (error) {
      setModelSupportsTools(false);
    } finally {
      setIsCheckingModel(false);
    }
  };

  // 加载所有可用工具（包括MCP工具）
  const loadTools = async () => {
    try {
      const tools = await getAllAvailableTools();
      setAllTools(tools);
    } catch (error) {
      console.error('加载工具失败:', error);
      setAllTools(availableTools);
    }
  };

  useEffect(() => {
    loadTools();
  }, []);

  // 当模型改变时检查工具支持
  useEffect(() => {
    if (selectedModel) {
      // 无论工具是否启用，都检查模型支持情况
      checkModelToolSupport(selectedModel);
    } else {
      setModelSupportsTools(null);
    }
  }, [selectedModel]); // 移除enableTools依赖，确保模型改变时总是检查

  // 处理工具开关切换
  const handleToolsToggle = async () => {
    if (!enableTools) {
      // 开启工具时，先检查模型支持
      if (modelSupportsTools === null && selectedModel) {
        await checkModelToolSupport(selectedModel);
      }

      // 如果模型不支持工具调用，阻止启用
      if (modelSupportsTools === false) {
        alert(`模型 ${selectedModel} 不支持工具调用功能，请选择其他模型。`);
        return;
      }

      // 如果还在检查中，等待检查完成
      if (isCheckingModel) {
        return;
      }
    }
    onToolsToggle(!enableTools);
  };

  // 处理工具选择
  const handleToolSelection = (toolName: string) => {
    const newSelectedTools = selectedTools.includes(toolName)
      ? selectedTools.filter(t => t !== toolName)
      : [...selectedTools, toolName];
    onSelectedToolsChange(newSelectedTools);
  };

  return {
    showToolSettings,
    setShowToolSettings,
    modelSupportsTools,
    isCheckingModel,
    allTools,
    handleToolsToggle,
    handleToolSelection,
  };
}