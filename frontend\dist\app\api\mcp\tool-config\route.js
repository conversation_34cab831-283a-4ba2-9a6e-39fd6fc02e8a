import { NextResponse } from 'next/server';
import Database from 'better-sqlite3';
import path from 'path';
const dbPath = path.join(process.cwd(), 'chat.db');
// 初始化数据库表
function initDatabase() {
    const db = new Database(dbPath);
    // 创建工具配置表
    db.exec(`
    CREATE TABLE IF NOT EXISTS mcp_tool_configs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tool_id INTEGER,
      server_name TEXT NOT NULL,
      tool_name TEXT NOT NULL,
      config TEXT NOT NULL, -- JSON格式存储配置
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(server_name, tool_name)
    )
  `);
    return db;
}
/**
 * POST /api/mcp/tool-config - 保存工具配置
 */
export async function POST(request) {
    try {
        const { toolId, serverName, toolName, config } = await request.json();
        if (!serverName || !toolName) {
            return NextResponse.json({ error: '服务器名称和工具名称不能为空' }, { status: 400 });
        }
        const db = initDatabase();
        try {
            // 检查是否已存在配置
            const existingConfig = db.prepare('SELECT id FROM mcp_tool_configs WHERE server_name = ? AND tool_name = ?').get(serverName, toolName);
            if (existingConfig) {
                // 更新现有配置
                db.prepare(`
          UPDATE mcp_tool_configs 
          SET config = ?, updated_at = CURRENT_TIMESTAMP
          WHERE server_name = ? AND tool_name = ?
        `).run(JSON.stringify(config), serverName, toolName);
            }
            else {
                // 插入新配置
                db.prepare(`
          INSERT INTO mcp_tool_configs (tool_id, server_name, tool_name, config)
          VALUES (?, ?, ?, ?)
        `).run(toolId || null, serverName, toolName, JSON.stringify(config));
            }
            return NextResponse.json({ success: true, message: '配置保存成功' });
        }
        finally {
            db.close();
        }
    }
    catch (error) {
        console.error('保存工具配置失败:', error);
        return NextResponse.json({ error: '保存配置失败' }, { status: 500 });
    }
}
/**
 * GET /api/mcp/tool-config?serverName=xxx&toolName=xxx - 获取工具配置
 */
export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const serverName = searchParams.get('serverName');
        const toolName = searchParams.get('toolName');
        if (!serverName || !toolName) {
            return NextResponse.json({ error: '服务器名称和工具名称不能为空' }, { status: 400 });
        }
        const db = initDatabase();
        try {
            const configRow = db.prepare('SELECT config FROM mcp_tool_configs WHERE server_name = ? AND tool_name = ?').get(serverName, toolName);
            if (configRow) {
                return NextResponse.json({
                    success: true,
                    config: JSON.parse(configRow.config)
                });
            }
            else {
                return NextResponse.json({
                    success: true,
                    config: {}
                });
            }
        }
        finally {
            db.close();
        }
    }
    catch (error) {
        console.error('获取工具配置失败:', error);
        return NextResponse.json({ error: '获取配置失败' }, { status: 500 });
    }
}
