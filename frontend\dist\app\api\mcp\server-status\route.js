import { NextResponse } from 'next/server';
import Database from 'better-sqlite3';
import path from 'path';
import { multiServerMcpClient } from '../../../../lib/mcp/mcp-multi-server-client';
const dbPath = path.join(process.cwd(), 'chat.db');
/**
 * 检查服务器连接状态API
 * POST /api/mcp/server-status
 */
export async function POST(request) {
    try {
        const { serverName } = await request.json();
        if (!serverName) {
            return NextResponse.json({ error: '服务器名称不能为空' }, { status: 400 });
        }
        let status = 'disconnected';
        let errorMessage = null;
        let toolCount = 0;
        // 处理本地服务器
        if (serverName === 'local') {
            try {
                const { mcpServerClient } = require('../../../../lib/mcp/mcp-client-server');
                const isConnected = mcpServerClient.isClientConnected();
                status = isConnected ? 'connected' : 'disconnected';
                if (isConnected) {
                    const tools = mcpServerClient.getAvailableTools();
                    toolCount = tools.length;
                }
            }
            catch (error) {
                status = 'error';
                errorMessage = error instanceof Error ? error.message : '连接失败';
            }
        }
        else {
            // 处理外部服务器
            const db = new Database(dbPath);
            try {
                // 获取服务器配置
                const server = db.prepare('SELECT * FROM mcp_servers WHERE name = ? AND enabled = 1').get(serverName);
                if (!server) {
                    return NextResponse.json({ error: '服务器不存在或已禁用' }, { status: 404 });
                }
                // 构建配置
                const config = {};
                config[server.name] = {
                    type: server.type,
                    ...(server.url && { url: server.url }),
                    ...(server.command && { command: server.command }),
                    ...(server.args && { args: JSON.parse(server.args || '[]') })
                };
                // 设置配置并尝试连接
                multiServerMcpClient.setConfig(config);
                try {
                    // 先连接所有服务器
                    await multiServerMcpClient.connectAll();
                    // 刷新工具列表
                    await multiServerMcpClient.refreshAllTools();
                    // 检查连接状态
                    const connectionStatus = multiServerMcpClient.getConnectionStatus();
                    if (connectionStatus[serverName]) {
                        // 获取工具列表
                        const tools = multiServerMcpClient.getToolsByServer(serverName);
                        status = 'connected';
                        toolCount = tools.length;
                    }
                    else {
                        status = 'disconnected';
                        toolCount = 0;
                    }
                    // 更新数据库中的状态
                    db.prepare(`
            UPDATE mcp_servers 
            SET status = ?, last_connected_at = datetime('now'), error_message = NULL 
            WHERE name = ?
          `).run('connected', serverName);
                }
                catch (error) {
                    status = 'error';
                    errorMessage = error instanceof Error ? error.message : '连接失败';
                    // 更新数据库中的错误状态
                    db.prepare(`
            UPDATE mcp_servers 
            SET status = ?, error_message = ? 
            WHERE name = ?
          `).run('error', errorMessage, serverName);
                }
            }
            finally {
                db.close();
            }
        }
        return NextResponse.json({
            success: true,
            serverName,
            status,
            toolCount,
            errorMessage
        });
    }
    catch (error) {
        console.error('检查服务器状态失败:', error);
        return NextResponse.json({ error: '检查服务器状态失败' }, { status: 500 });
    }
}
