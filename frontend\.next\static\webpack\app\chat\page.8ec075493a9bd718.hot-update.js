"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/hooks/useToolSettings.ts":
/*!***********************************************!*\
  !*** ./src/app/chat/hooks/useToolSettings.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToolSettings: () => (/* binding */ useToolSettings)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/tools */ \"(app-pages-browser)/./src/lib/tools.ts\");\n/* __next_internal_client_entry_do_not_use__ useToolSettings auto */ \n\nfunction useToolSettings(param) {\n    let { selectedModel, enableTools, selectedTools, onToolsToggle, onSelectedToolsChange } = param;\n    const [showToolSettings, setShowToolSettings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [modelSupportsTools, setModelSupportsTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isCheckingModel, setIsCheckingModel] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [allTools, setAllTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(_lib_tools__WEBPACK_IMPORTED_MODULE_1__.availableTools);\n    // 验证模型是否支持工具调用\n    const checkModelToolSupport = async (model)=>{\n        if (!model) return;\n        setIsCheckingModel(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: model,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: 'test'\n                        }\n                    ],\n                    enableTools: true,\n                    testMode: true\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setModelSupportsTools(data.supportsTools);\n            } else {\n                setModelSupportsTools(false);\n            }\n        } catch (error) {\n            setModelSupportsTools(false);\n        } finally{\n            setIsCheckingModel(false);\n        }\n    };\n    // 加载所有可用工具（包括MCP工具）\n    const loadTools = async ()=>{\n        try {\n            const tools = await (0,_lib_tools__WEBPACK_IMPORTED_MODULE_1__.getAllAvailableTools)();\n            setAllTools(tools);\n        } catch (error) {\n            console.error('加载工具失败:', error);\n            setAllTools(_lib_tools__WEBPACK_IMPORTED_MODULE_1__.availableTools);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useToolSettings.useEffect\": ()=>{\n            loadTools();\n        }\n    }[\"useToolSettings.useEffect\"], []);\n    // 当模型改变时检查工具支持\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useToolSettings.useEffect\": ()=>{\n            if (selectedModel) {\n                // 无论工具是否启用，都检查模型支持情况\n                checkModelToolSupport(selectedModel);\n            } else {\n                setModelSupportsTools(null);\n            }\n        }\n    }[\"useToolSettings.useEffect\"], [\n        selectedModel\n    ]); // 移除enableTools依赖，确保模型改变时总是检查\n    // 处理工具开关切换\n    const handleToolsToggle = async ()=>{\n        if (!enableTools) {\n            // 开启工具时，先检查模型支持\n            if (modelSupportsTools === null) {\n                await checkModelToolSupport(selectedModel);\n            }\n            if (modelSupportsTools === false) {\n                alert(\"模型 \".concat(selectedModel, \" 不支持工具调用功能，请选择其他模型。\"));\n                return;\n            }\n        }\n        onToolsToggle(!enableTools);\n    };\n    // 处理工具选择\n    const handleToolSelection = (toolName)=>{\n        const newSelectedTools = selectedTools.includes(toolName) ? selectedTools.filter((t)=>t !== toolName) : [\n            ...selectedTools,\n            toolName\n        ];\n        onSelectedToolsChange(newSelectedTools);\n    };\n    return {\n        showToolSettings,\n        setShowToolSettings,\n        modelSupportsTools,\n        isCheckingModel,\n        allTools,\n        handleToolsToggle,\n        handleToolSelection\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9ob29rcy91c2VUb29sU2V0dGluZ3MudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztxRUFFNEM7QUFDdUI7QUFXNUQsU0FBU0ksZ0JBQWdCLEtBTVQ7UUFOUyxFQUM5QkMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxxQkFBcUIsRUFDQSxHQU5TO0lBTzlCLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR1gsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDWSxvQkFBb0JDLHNCQUFzQixHQUFHYiwrQ0FBUUEsQ0FBaUI7SUFDN0UsTUFBTSxDQUFDYyxpQkFBaUJDLG1CQUFtQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNnQixVQUFVQyxZQUFZLEdBQUdqQiwrQ0FBUUEsQ0FBU0Usc0RBQWNBO0lBRS9ELGVBQWU7SUFDZixNQUFNZ0Isd0JBQXdCLE9BQU9DO1FBQ25DLElBQUksQ0FBQ0EsT0FBTztRQUVaSixtQkFBbUI7UUFDbkIsSUFBSTtZQUNGLE1BQU1LLFdBQVcsTUFBTUMsTUFBTSxhQUFhO2dCQUN4Q0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CUCxPQUFPQTtvQkFDUFEsVUFBVTt3QkFBQzs0QkFBRUMsTUFBTTs0QkFBUUMsU0FBUzt3QkFBTztxQkFBRTtvQkFDN0N2QixhQUFhO29CQUNid0IsVUFBVTtnQkFDWjtZQUNGO1lBRUEsTUFBTUMsT0FBTyxNQUFNWCxTQUFTWSxJQUFJO1lBQ2hDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJwQixzQkFBc0JrQixLQUFLRyxhQUFhO1lBQzFDLE9BQU87Z0JBQ0xyQixzQkFBc0I7WUFDeEI7UUFDRixFQUFFLE9BQU9zQixPQUFPO1lBQ2R0QixzQkFBc0I7UUFDeEIsU0FBVTtZQUNSRSxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNcUIsWUFBWTtRQUNoQixJQUFJO1lBQ0YsTUFBTUMsUUFBUSxNQUFNbEMsZ0VBQW9CQTtZQUN4Q2MsWUFBWW9CO1FBQ2QsRUFBRSxPQUFPRixPQUFPO1lBQ2RHLFFBQVFILEtBQUssQ0FBQyxXQUFXQTtZQUN6QmxCLFlBQVlmLHNEQUFjQTtRQUM1QjtJQUNGO0lBRUFELGdEQUFTQTtxQ0FBQztZQUNSbUM7UUFDRjtvQ0FBRyxFQUFFO0lBRUwsZUFBZTtJQUNmbkMsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSUksZUFBZTtnQkFDakIscUJBQXFCO2dCQUNyQmEsc0JBQXNCYjtZQUN4QixPQUFPO2dCQUNMUSxzQkFBc0I7WUFDeEI7UUFDRjtvQ0FBRztRQUFDUjtLQUFjLEdBQUcsOEJBQThCO0lBRW5ELFdBQVc7SUFDWCxNQUFNa0Msb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ2pDLGFBQWE7WUFDaEIsZ0JBQWdCO1lBQ2hCLElBQUlNLHVCQUF1QixNQUFNO2dCQUMvQixNQUFNTSxzQkFBc0JiO1lBQzlCO1lBQ0EsSUFBSU8sdUJBQXVCLE9BQU87Z0JBQ2hDNEIsTUFBTSxNQUFvQixPQUFkbkMsZUFBYztnQkFDMUI7WUFDRjtRQUNGO1FBQ0FHLGNBQWMsQ0FBQ0Y7SUFDakI7SUFFQSxTQUFTO0lBQ1QsTUFBTW1DLHNCQUFzQixDQUFDQztRQUMzQixNQUFNQyxtQkFBbUJwQyxjQUFjcUMsUUFBUSxDQUFDRixZQUM1Q25DLGNBQWNzQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLE1BQU1KLFlBQ2hDO2VBQUluQztZQUFlbUM7U0FBUztRQUNoQ2pDLHNCQUFzQmtDO0lBQ3hCO0lBRUEsT0FBTztRQUNMakM7UUFDQUM7UUFDQUM7UUFDQUU7UUFDQUU7UUFDQXVCO1FBQ0FFO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGNoYXRcXGhvb2tzXFx1c2VUb29sU2V0dGluZ3MudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgYXZhaWxhYmxlVG9vbHMsIGdldEFsbEF2YWlsYWJsZVRvb2xzIH0gZnJvbSAnQC9saWIvdG9vbHMnO1xuaW1wb3J0IHsgVG9vbCB9IGZyb20gJ0AvbGliL29sbGFtYSc7XG5cbmludGVyZmFjZSBVc2VUb29sU2V0dGluZ3NQcm9wcyB7XG4gIHNlbGVjdGVkTW9kZWw6IHN0cmluZztcbiAgZW5hYmxlVG9vbHM6IGJvb2xlYW47XG4gIHNlbGVjdGVkVG9vbHM6IHN0cmluZ1tdO1xuICBvblRvb2xzVG9nZ2xlOiAoZW5hYmxlZDogYm9vbGVhbikgPT4gdm9pZDtcbiAgb25TZWxlY3RlZFRvb2xzQ2hhbmdlOiAodG9vbHM6IHN0cmluZ1tdKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVG9vbFNldHRpbmdzKHtcbiAgc2VsZWN0ZWRNb2RlbCxcbiAgZW5hYmxlVG9vbHMsXG4gIHNlbGVjdGVkVG9vbHMsXG4gIG9uVG9vbHNUb2dnbGUsXG4gIG9uU2VsZWN0ZWRUb29sc0NoYW5nZSxcbn06IFVzZVRvb2xTZXR0aW5nc1Byb3BzKSB7XG4gIGNvbnN0IFtzaG93VG9vbFNldHRpbmdzLCBzZXRTaG93VG9vbFNldHRpbmdzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21vZGVsU3VwcG9ydHNUb29scywgc2V0TW9kZWxTdXBwb3J0c1Rvb2xzXSA9IHVzZVN0YXRlPGJvb2xlYW4gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzQ2hlY2tpbmdNb2RlbCwgc2V0SXNDaGVja2luZ01vZGVsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2FsbFRvb2xzLCBzZXRBbGxUb29sc10gPSB1c2VTdGF0ZTxUb29sW10+KGF2YWlsYWJsZVRvb2xzKTtcblxuICAvLyDpqozor4HmqKHlnovmmK/lkKbmlK/mjIHlt6XlhbfosIPnlKhcbiAgY29uc3QgY2hlY2tNb2RlbFRvb2xTdXBwb3J0ID0gYXN5bmMgKG1vZGVsOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIW1vZGVsKSByZXR1cm47XG4gICAgXG4gICAgc2V0SXNDaGVja2luZ01vZGVsKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2NoYXQnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIG1vZGVsOiBtb2RlbCxcbiAgICAgICAgICBtZXNzYWdlczogW3sgcm9sZTogJ3VzZXInLCBjb250ZW50OiAndGVzdCcgfV0sXG4gICAgICAgICAgZW5hYmxlVG9vbHM6IHRydWUsXG4gICAgICAgICAgdGVzdE1vZGU6IHRydWUsIC8vIOa3u+WKoOa1i+ivleaooeW8j+agh+ivhlxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRNb2RlbFN1cHBvcnRzVG9vbHMoZGF0YS5zdXBwb3J0c1Rvb2xzKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldE1vZGVsU3VwcG9ydHNUb29scyhmYWxzZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldE1vZGVsU3VwcG9ydHNUb29scyhmYWxzZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzQ2hlY2tpbmdNb2RlbChmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWKoOi9veaJgOacieWPr+eUqOW3peWFt++8iOWMheaLrE1DUOW3peWFt++8iVxuICBjb25zdCBsb2FkVG9vbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRvb2xzID0gYXdhaXQgZ2V0QWxsQXZhaWxhYmxlVG9vbHMoKTtcbiAgICAgIHNldEFsbFRvb2xzKHRvb2xzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295bel5YW35aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHNldEFsbFRvb2xzKGF2YWlsYWJsZVRvb2xzKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkVG9vbHMoKTtcbiAgfSwgW10pO1xuXG4gIC8vIOW9k+aooeWei+aUueWPmOaXtuajgOafpeW3peWFt+aUr+aMgVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZE1vZGVsKSB7XG4gICAgICAvLyDml6Dorrrlt6XlhbfmmK/lkKblkK/nlKjvvIzpg73mo4Dmn6XmqKHlnovmlK/mjIHmg4XlhrVcbiAgICAgIGNoZWNrTW9kZWxUb29sU3VwcG9ydChzZWxlY3RlZE1vZGVsKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0TW9kZWxTdXBwb3J0c1Rvb2xzKG51bGwpO1xuICAgIH1cbiAgfSwgW3NlbGVjdGVkTW9kZWxdKTsgLy8g56e76ZmkZW5hYmxlVG9vbHPkvp3otZbvvIznoa7kv53mqKHlnovmlLnlj5jml7bmgLvmmK/mo4Dmn6VcblxuICAvLyDlpITnkIblt6XlhbflvIDlhbPliIfmjaJcbiAgY29uc3QgaGFuZGxlVG9vbHNUb2dnbGUgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFlbmFibGVUb29scykge1xuICAgICAgLy8g5byA5ZCv5bel5YW35pe277yM5YWI5qOA5p+l5qih5Z6L5pSv5oyBXG4gICAgICBpZiAobW9kZWxTdXBwb3J0c1Rvb2xzID09PSBudWxsKSB7XG4gICAgICAgIGF3YWl0IGNoZWNrTW9kZWxUb29sU3VwcG9ydChzZWxlY3RlZE1vZGVsKTtcbiAgICAgIH1cbiAgICAgIGlmIChtb2RlbFN1cHBvcnRzVG9vbHMgPT09IGZhbHNlKSB7XG4gICAgICAgIGFsZXJ0KGDmqKHlnosgJHtzZWxlY3RlZE1vZGVsfSDkuI3mlK/mjIHlt6XlhbfosIPnlKjlip/og73vvIzor7fpgInmi6nlhbbku5bmqKHlnovjgIJgKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgICBvblRvb2xzVG9nZ2xlKCFlbmFibGVUb29scyk7XG4gIH07XG5cbiAgLy8g5aSE55CG5bel5YW36YCJ5oupXG4gIGNvbnN0IGhhbmRsZVRvb2xTZWxlY3Rpb24gPSAodG9vbE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5ld1NlbGVjdGVkVG9vbHMgPSBzZWxlY3RlZFRvb2xzLmluY2x1ZGVzKHRvb2xOYW1lKVxuICAgICAgPyBzZWxlY3RlZFRvb2xzLmZpbHRlcih0ID0+IHQgIT09IHRvb2xOYW1lKVxuICAgICAgOiBbLi4uc2VsZWN0ZWRUb29scywgdG9vbE5hbWVdO1xuICAgIG9uU2VsZWN0ZWRUb29sc0NoYW5nZShuZXdTZWxlY3RlZFRvb2xzKTtcbiAgfTtcblxuICByZXR1cm4ge1xuICAgIHNob3dUb29sU2V0dGluZ3MsXG4gICAgc2V0U2hvd1Rvb2xTZXR0aW5ncyxcbiAgICBtb2RlbFN1cHBvcnRzVG9vbHMsXG4gICAgaXNDaGVja2luZ01vZGVsLFxuICAgIGFsbFRvb2xzLFxuICAgIGhhbmRsZVRvb2xzVG9nZ2xlLFxuICAgIGhhbmRsZVRvb2xTZWxlY3Rpb24sXG4gIH07XG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiYXZhaWxhYmxlVG9vbHMiLCJnZXRBbGxBdmFpbGFibGVUb29scyIsInVzZVRvb2xTZXR0aW5ncyIsInNlbGVjdGVkTW9kZWwiLCJlbmFibGVUb29scyIsInNlbGVjdGVkVG9vbHMiLCJvblRvb2xzVG9nZ2xlIiwib25TZWxlY3RlZFRvb2xzQ2hhbmdlIiwic2hvd1Rvb2xTZXR0aW5ncyIsInNldFNob3dUb29sU2V0dGluZ3MiLCJtb2RlbFN1cHBvcnRzVG9vbHMiLCJzZXRNb2RlbFN1cHBvcnRzVG9vbHMiLCJpc0NoZWNraW5nTW9kZWwiLCJzZXRJc0NoZWNraW5nTW9kZWwiLCJhbGxUb29scyIsInNldEFsbFRvb2xzIiwiY2hlY2tNb2RlbFRvb2xTdXBwb3J0IiwibW9kZWwiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibWVzc2FnZXMiLCJyb2xlIiwiY29udGVudCIsInRlc3RNb2RlIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwic3VwcG9ydHNUb29scyIsImVycm9yIiwibG9hZFRvb2xzIiwidG9vbHMiLCJjb25zb2xlIiwiaGFuZGxlVG9vbHNUb2dnbGUiLCJhbGVydCIsImhhbmRsZVRvb2xTZWxlY3Rpb24iLCJ0b29sTmFtZSIsIm5ld1NlbGVjdGVkVG9vbHMiLCJpbmNsdWRlcyIsImZpbHRlciIsInQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/hooks/useToolSettings.ts\n"));

/***/ })

});