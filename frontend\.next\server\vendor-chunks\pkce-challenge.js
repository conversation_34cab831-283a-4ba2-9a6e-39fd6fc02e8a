"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pkce-challenge";
exports.ids = ["vendor-chunks/pkce-challenge"];
exports.modules = {

/***/ "(rsc)/./node_modules/pkce-challenge/dist/index.node.js":
/*!********************************************************!*\
  !*** ./node_modules/pkce-challenge/dist/index.node.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pkceChallenge),\n/* harmony export */   generateChallenge: () => (/* binding */ generateChallenge),\n/* harmony export */   verifyChallenge: () => (/* binding */ verifyChallenge)\n/* harmony export */ });\nlet crypto;\ncrypto =\n    globalThis.crypto?.webcrypto ?? // Node.js [18-16] REPL\n        globalThis.crypto ?? // Node.js >18\n        Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:crypto */ \"node:crypto\", 19)).then(m => m.webcrypto); // Node.js <18 Non-REPL\n/**\n * Creates an array of length `size` of random bytes\n * @param size\n * @returns Array of random ints (0 to 255)\n */\nasync function getRandomValues(size) {\n    return (await crypto).getRandomValues(new Uint8Array(size));\n}\n/** Generate cryptographically strong random string\n * @param size The desired length of the string\n * @returns The random string\n */\nasync function random(size) {\n    const mask = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~\";\n    let result = \"\";\n    const randomUints = await getRandomValues(size);\n    for (let i = 0; i < size; i++) {\n        // cap the value of the randomIndex to mask.length - 1\n        const randomIndex = randomUints[i] % mask.length;\n        result += mask[randomIndex];\n    }\n    return result;\n}\n/** Generate a PKCE challenge verifier\n * @param length Length of the verifier\n * @returns A random verifier `length` characters long\n */\nasync function generateVerifier(length) {\n    return await random(length);\n}\n/** Generate a PKCE code challenge from a code verifier\n * @param code_verifier\n * @returns The base64 url encoded code challenge\n */\nasync function generateChallenge(code_verifier) {\n    const buffer = await (await crypto).subtle.digest(\"SHA-256\", new TextEncoder().encode(code_verifier));\n    // Generate base64url string\n    // btoa is deprecated in Node.js but is used here for web browser compatibility\n    // (which has no good replacement yet, see also https://github.com/whatwg/html/issues/6811)\n    return btoa(String.fromCharCode(...new Uint8Array(buffer)))\n        .replace(/\\//g, '_')\n        .replace(/\\+/g, '-')\n        .replace(/=/g, '');\n}\n/** Generate a PKCE challenge pair\n * @param length Length of the verifer (between 43-128). Defaults to 43.\n * @returns PKCE challenge pair\n */\nasync function pkceChallenge(length) {\n    if (!length)\n        length = 43;\n    if (length < 43 || length > 128) {\n        throw `Expected a length between 43 and 128. Received ${length}.`;\n    }\n    const verifier = await generateVerifier(length);\n    const challenge = await generateChallenge(verifier);\n    return {\n        code_verifier: verifier,\n        code_challenge: challenge,\n    };\n}\n/** Verify that a code_verifier produces the expected code challenge\n * @param code_verifier\n * @param expectedChallenge The code challenge to verify\n * @returns True if challenges are equal. False otherwise.\n */\nasync function verifyChallenge(code_verifier, expectedChallenge) {\n    const actualChallenge = await generateChallenge(code_verifier);\n    return actualChallenge === expectedChallenge;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pkce-challenge/dist/index.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pkce-challenge/dist/index.node.js":
/*!********************************************************!*\
  !*** ./node_modules/pkce-challenge/dist/index.node.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pkceChallenge),\n/* harmony export */   generateChallenge: () => (/* binding */ generateChallenge),\n/* harmony export */   verifyChallenge: () => (/* binding */ verifyChallenge)\n/* harmony export */ });\nlet crypto;\ncrypto =\n    globalThis.crypto?.webcrypto ?? // Node.js [18-16] REPL\n        globalThis.crypto ?? // Node.js >18\n        Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:crypto */ \"node:crypto\", 19)).then(m => m.webcrypto); // Node.js <18 Non-REPL\n/**\n * Creates an array of length `size` of random bytes\n * @param size\n * @returns Array of random ints (0 to 255)\n */\nasync function getRandomValues(size) {\n    return (await crypto).getRandomValues(new Uint8Array(size));\n}\n/** Generate cryptographically strong random string\n * @param size The desired length of the string\n * @returns The random string\n */\nasync function random(size) {\n    const mask = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~\";\n    let result = \"\";\n    const randomUints = await getRandomValues(size);\n    for (let i = 0; i < size; i++) {\n        // cap the value of the randomIndex to mask.length - 1\n        const randomIndex = randomUints[i] % mask.length;\n        result += mask[randomIndex];\n    }\n    return result;\n}\n/** Generate a PKCE challenge verifier\n * @param length Length of the verifier\n * @returns A random verifier `length` characters long\n */\nasync function generateVerifier(length) {\n    return await random(length);\n}\n/** Generate a PKCE code challenge from a code verifier\n * @param code_verifier\n * @returns The base64 url encoded code challenge\n */\nasync function generateChallenge(code_verifier) {\n    const buffer = await (await crypto).subtle.digest(\"SHA-256\", new TextEncoder().encode(code_verifier));\n    // Generate base64url string\n    // btoa is deprecated in Node.js but is used here for web browser compatibility\n    // (which has no good replacement yet, see also https://github.com/whatwg/html/issues/6811)\n    return btoa(String.fromCharCode(...new Uint8Array(buffer)))\n        .replace(/\\//g, '_')\n        .replace(/\\+/g, '-')\n        .replace(/=/g, '');\n}\n/** Generate a PKCE challenge pair\n * @param length Length of the verifer (between 43-128). Defaults to 43.\n * @returns PKCE challenge pair\n */\nasync function pkceChallenge(length) {\n    if (!length)\n        length = 43;\n    if (length < 43 || length > 128) {\n        throw `Expected a length between 43 and 128. Received ${length}.`;\n    }\n    const verifier = await generateVerifier(length);\n    const challenge = await generateChallenge(verifier);\n    return {\n        code_verifier: verifier,\n        code_challenge: challenge,\n    };\n}\n/** Verify that a code_verifier produces the expected code challenge\n * @param code_verifier\n * @param expectedChallenge The code challenge to verify\n * @returns True if challenges are equal. False otherwise.\n */\nasync function verifyChallenge(code_verifier, expectedChallenge) {\n    const actualChallenge = await generateChallenge(code_verifier);\n    return actualChallenge === expectedChallenge;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pkce-challenge/dist/index.node.js\n");

/***/ })

};
;