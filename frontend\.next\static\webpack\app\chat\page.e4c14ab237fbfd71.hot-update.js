"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Activity)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Activity\", [\n    [\n        \"path\",\n        {\n            d: \"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2\",\n            key: \"169zse\"\n        }\n    ]\n]);\n //# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Zap\", [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n]);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* harmony import */ var _ToolCallVisualization__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToolCallVisualization */ \"(app-pages-browser)/./src/app/chat/components/ToolCallVisualization.tsx\");\n/* harmony import */ var _ToolLoadingAnimation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ToolLoadingAnimation */ \"(app-pages-browser)/./src/app/chat/components/ToolLoadingAnimation.tsx\");\n/* harmony import */ var _ToolContentDisplay__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ToolContentDisplay */ \"(app-pages-browser)/./src/app/chat/components/ToolContentDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \n\n\n\n\n\n\nfunction MessageItem(param) {\n    let { message, showAIStatus, aiState } = param;\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    // 解析工具调用内容，创建步骤数据\n    const parseToolCallSteps = (content)=>{\n        const steps = [];\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') && content.includes('参数:') && content.includes('结果:')) {\n            const toolCallMatches = content.match(/调用工具:\\s*([^\\n]+)\\n参数:\\s*([^]*?)\\n\\n结果:\\s*([^]*?)(?=\\n\\n|$)/g);\n            if (toolCallMatches) {\n                toolCallMatches.forEach((match, index)=>{\n                    const toolNameMatch = match.match(/调用工具:\\s*([^\\n]+)/);\n                    const paramsMatch = match.match(/参数:\\s*([^]*?)\\n\\n结果:/);\n                    const resultMatch = match.match(/结果:\\s*([^]*?)(?=\\n\\n|$)/);\n                    if (toolNameMatch) {\n                        const toolName = toolNameMatch[1].trim();\n                        let params = {};\n                        let result = '';\n                        try {\n                            if (paramsMatch) {\n                                params = JSON.parse(paramsMatch[1].trim());\n                            }\n                        } catch (e) {\n                            var _paramsMatch_;\n                            params = {\n                                raw: (paramsMatch === null || paramsMatch === void 0 ? void 0 : (_paramsMatch_ = paramsMatch[1]) === null || _paramsMatch_ === void 0 ? void 0 : _paramsMatch_.trim()) || ''\n                            };\n                        }\n                        if (resultMatch) {\n                            result = resultMatch[1].trim();\n                        }\n                        // 添加执行步骤\n                        steps.push({\n                            id: \"execution-\".concat(index),\n                            type: 'execution',\n                            toolName,\n                            status: 'success',\n                            timestamp: new Date(),\n                            input: params\n                        });\n                        // 添加结果步骤\n                        steps.push({\n                            id: \"result-\".concat(index),\n                            type: 'result',\n                            toolName,\n                            status: result.includes('错误') || result.includes('失败') ? 'error' : 'success',\n                            timestamp: new Date(),\n                            output: result,\n                            error: result.includes('错误') || result.includes('失败') ? result : undefined\n                        });\n                    }\n                });\n            }\n        }\n        return steps;\n    };\n    // 检测并渲染图片URL\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 116,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理工具调用结果的显示\n    const renderToolContent = (content)=>{\n        try {\n            const toolData = JSON.parse(content);\n            if (toolData.tool_name && toolData.result) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_5__.ToolResultSummary, {\n                    result: toolData.result,\n                    isSuccess: !content.includes('错误') && !content.includes('失败'),\n                    executionTime: toolData.execution_time\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this);\n            }\n        } catch (e) {\n            // 如果不是JSON格式，检查是否是简单的工具结果\n            if (content.includes('工具执行') || content.includes('调用工具')) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_5__.ToolContentDisplay, {\n                    content: content,\n                    type: \"output\",\n                    title: \"工具执行结果\",\n                    maxLines: 6\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 145,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息中的工具调用\n    const renderAssistantContent = (content)=>{\n        // 检查是否包含工具调用信息\n        if (content.includes('调用工具:') || content.includes('```json') && content.includes('tool_calls')) {\n            const toolSteps = parseToolCallSteps(content);\n            if (toolSteps.length > 0) {\n                // 分离工具调用部分和普通文本部分\n                const toolCallPattern = /调用工具:[^]*?(?=\\n\\n(?!参数:|结果:)|$)/g;\n                const textParts = content.split(toolCallPattern).filter((part)=>part.trim());\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolCallVisualization__WEBPACK_IMPORTED_MODULE_3__.ToolCallVisualization, {\n                            steps: toolSteps,\n                            isLoading: false\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        textParts.map((part, index)=>part.trim() && !part.includes('调用工具:') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(part.trim())\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 17\n                            }, this) : null)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this);\n            }\n            // 处理旧格式的JSON工具调用\n            if (content.includes('```json') && content.includes('tool_calls')) {\n                const parts = content.split('```');\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: parts.map((part, index)=>{\n                        if (part.startsWith('json') && part.includes('tool_calls')) {\n                            try {\n                                var _toolCall_tool_calls__function, _toolCall_tool_calls_, _toolCall_tool_calls, _toolCall_tool_calls__function1, _toolCall_tool_calls_1, _toolCall_tool_calls1;\n                                const jsonContent = part.replace('json\\n', '').trim();\n                                const toolCall = JSON.parse(jsonContent);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolContentDisplay__WEBPACK_IMPORTED_MODULE_5__.ToolContentDisplay, {\n                                    content: ((_toolCall_tool_calls = toolCall.tool_calls) === null || _toolCall_tool_calls === void 0 ? void 0 : (_toolCall_tool_calls_ = _toolCall_tool_calls[0]) === null || _toolCall_tool_calls_ === void 0 ? void 0 : (_toolCall_tool_calls__function = _toolCall_tool_calls_.function) === null || _toolCall_tool_calls__function === void 0 ? void 0 : _toolCall_tool_calls__function.arguments) || {},\n                                    type: \"input\",\n                                    title: \"调用工具: \".concat((_toolCall_tool_calls1 = toolCall.tool_calls) === null || _toolCall_tool_calls1 === void 0 ? void 0 : (_toolCall_tool_calls_1 = _toolCall_tool_calls1[0]) === null || _toolCall_tool_calls_1 === void 0 ? void 0 : (_toolCall_tool_calls__function1 = _toolCall_tool_calls_1.function) === null || _toolCall_tool_calls__function1 === void 0 ? void 0 : _toolCall_tool_calls__function1.name),\n                                    maxLines: 4\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 21\n                                }, this);\n                            } catch (e) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"whitespace-pre-wrap\",\n                                    children: part\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 26\n                                }, this);\n                            }\n                        }\n                        return part.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap\",\n                            children: renderImageIfUrl(part)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 36\n                        }, this) : null;\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"whitespace-pre-wrap\",\n            children: renderImageIfUrl(content)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 209,\n            columnNumber: 12\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 \".concat(isUser ? 'bg-blue-600 text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'),\n                        children: [\n                            showAIStatus && aiState && aiState.status === 'tool_calling' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToolLoadingAnimation__WEBPACK_IMPORTED_MODULE_4__.ToolLoadingAnimation, {\n                                    toolName: aiState.toolName,\n                                    variant: \"default\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(message.content)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 317,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/components/ToolLoadingAnimation.tsx":
/*!**********************************************************!*\
  !*** ./src/app/chat/components/ToolLoadingAnimation.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolExecutionIndicator: () => (/* binding */ ToolExecutionIndicator),\n/* harmony export */   ToolLoadingAnimation: () => (/* binding */ ToolLoadingAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Cog,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Cog,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Cog,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ ToolLoadingAnimation,ToolExecutionIndicator auto */ \n\n\nfunction ToolLoadingAnimation(param) {\n    let { toolName, variant = 'default', showProgress = false, progress = 0 } = param;\n    if (variant === 'compact') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-4 h-4 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-blue-200 rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-blue-600\",\n                    children: toolName ? \"执行 \".concat(toolName, \"...\") : '执行中...'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (variant === 'detailed') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-4 border-blue-200 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-4 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-12 h-12 bg-blue-400 rounded-full opacity-20 animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4 text-yellow-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-800 dark:text-blue-200\",\n                                        children: \"MCP工具调用中\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            toolName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-700 dark:text-blue-300 mb-2\",\n                                children: [\n                                    \"正在执行: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            showProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\",\n                                    style: {\n                                        width: \"\".concat(Math.min(100, Math.max(0, progress)), \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Cog_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-3 h-3 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-blue-500 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-blue-500 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: '0.1s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1 h-1 bg-blue-500 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: '0.2s'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-blue-600 dark:text-blue-400 ml-2\",\n                                        children: \"处理中...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    // 默认样式\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-6 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 border-2 border-blue-200 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-medium text-sm text-blue-800 dark:text-blue-200\",\n                        children: toolName ? \"正在执行: \".concat(toolName) : '正在调用工具'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-600 dark:text-blue-300\",\n                        children: \"请稍候...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 ml-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-4 bg-blue-400 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-6 bg-blue-500 rounded-full animate-pulse\",\n                        style: {\n                            animationDelay: '0.1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-4 bg-blue-400 rounded-full animate-pulse\",\n                        style: {\n                            animationDelay: '0.2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_c = ToolLoadingAnimation;\n// 简化的工具执行状态指示器\nfunction ToolExecutionIndicator(param) {\n    let { toolName, status = 'running' } = param;\n    const getStatusConfig = ()=>{\n        switch(status){\n            case 'running':\n                return {\n                    color: 'text-blue-600',\n                    bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n                    borderColor: 'border-blue-200 dark:border-blue-700',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 border-2 border-blue-200 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 border-2 border-blue-600 rounded-full border-t-transparent animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, this)\n                };\n            case 'success':\n                return {\n                    color: 'text-green-600',\n                    bgColor: 'bg-green-50 dark:bg-green-900/20',\n                    borderColor: 'border-green-200 dark:border-green-700',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 bg-green-600 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 bg-white rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 17\n                    }, this)\n                };\n            case 'error':\n                return {\n                    color: 'text-red-600',\n                    bgColor: 'bg-red-50 dark:bg-red-900/20',\n                    borderColor: 'border-red-200 dark:border-red-700',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 bg-red-600 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-1 bg-white rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 17\n                    }, this)\n                };\n            default:\n                return {\n                    color: 'text-gray-600',\n                    bgColor: 'bg-gray-50 dark:bg-gray-900/20',\n                    borderColor: 'border-gray-200 dark:border-gray-700',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 bg-gray-400 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 17\n                    }, this)\n                };\n        }\n    };\n    const config = getStatusConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center gap-2 px-3 py-1 rounded-full border \".concat(config.bgColor, \" \").concat(config.borderColor),\n        children: [\n            config.icon,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium \".concat(config.color),\n                children: toolName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\ToolLoadingAnimation.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ToolExecutionIndicator;\nvar _c, _c1;\n$RefreshReg$(_c, \"ToolLoadingAnimation\");\n$RefreshReg$(_c1, \"ToolExecutionIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/ToolLoadingAnimation.tsx\n"));

/***/ })

});